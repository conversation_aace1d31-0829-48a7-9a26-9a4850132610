# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configure display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette('husl')

# Load the dataset
file_path = '../../../../../data/real_test_datasets/claim_anonymized.csv'
df = pd.read_csv(file_path)

print("📊 DATASET OVERVIEW")
print("=" * 50)
print(f"Total records: {len(df):,}")
print(f"Total columns: {df.shape[1]}")

# Check column names and data types
print("📋 COLUMN INFORMATION")
print("=" * 50)
print(f"Column names:")
for i, col in enumerate(df.columns, 1):
    print(f"{i:2d}. {col} ({df[col].dtype})")

# Quick sample of the data
print("👀 SAMPLE DATA (First 3 rows)")
print("=" * 50)
display(df.head(3).T)

# Check for missing values
print("🔍 MISSING DATA ANALYSIS")
print("=" * 50)

missing_data = df.isnull().sum()
missing_pct = (missing_data / len(df)) * 100

missing_summary = pd.DataFrame({
    'Missing_Count': missing_data,
    'Missing_Percentage': missing_pct
}).sort_values('Missing_Count', ascending=False)

# Show only columns with missing data
missing_with_nulls = missing_summary[missing_summary['Missing_Count'] > 0]

if len(missing_with_nulls) > 0:
    print("Columns with missing data:")
    for col, row in missing_with_nulls.iterrows():
        print(f"  • {col}: {row['Missing_Count']:,} ({row['Missing_Percentage']:.1f}%)")
else:
    print("✅ No missing data found!")

# Check for duplicate records
print("🔄 DUPLICATE ANALYSIS")
print("=" * 50)

total_duplicates = df.duplicated().sum()
print(f"Exact duplicate rows: {total_duplicates:,}")

# Check for duplicate activity IDs (should be unique)
if 'activity_id' in df.columns:
    duplicate_activity_ids = df['activity_id'].duplicated().sum()
    print(f"Duplicate activity IDs: {duplicate_activity_ids:,}")
    
    if duplicate_activity_ids == 0:
        print("✅ All activity IDs are unique")
    else:
        print("⚠️  Some activity IDs are duplicated")

# number o of unique cases
print("🔢 UNIQUE CASES")
print("=" * 50)
unique_cases = df['case'].nunique()
print(f"Total unique cases: {unique_cases:,}")
# Display unique cases
print("Unique cases in 'case' column:")
print(df['case'].unique())

# Analyze patients and cases
print("👥 PATIENT ANALYSIS")
print("=" * 50)

unique_patients = df['aio_patient_id'].nunique()
unique_cases = df['case'].nunique() 
total_activities = len(df)

print(f"Unique patients: {unique_patients:,}")
print(f"Unique cases/encounters: {unique_cases:,}")
print(f"Total activities/procedures: {total_activities:,}")
print()
print(f"Average activities per patient: {total_activities / unique_patients:.1f}")
print(f"Average activities per case: {total_activities / unique_cases:.1f}")
print(f"Average cases per patient: {unique_cases / unique_patients:.1f}")

# Analyze activity distribution per patient
activities_per_patient = df.groupby('aio_patient_id').size()

print("📈 ACTIVITIES PER PATIENT DISTRIBUTION")
print("=" * 50)
print(f"Minimum activities: {activities_per_patient.min()}")
print(f"Maximum activities: {activities_per_patient.max()}")
print(f"Median activities: {activities_per_patient.median():.0f}")
print(f"Mean activities: {activities_per_patient.mean():.1f}")

# Create a simple histogram
plt.figure(figsize=(10, 3))
plt.hist(activities_per_patient, bins=30, alpha=0.7)
plt.title('Distribution of Activities per Patient')
plt.xlabel('Number of Activities')
plt.ylabel('Number of Patients')
plt.grid(True, alpha=0.3)
plt.show()

# Analyze medical codes
print("🩺 MEDICAL CODES ANALYSIS")
print("=" * 50)

unique_codes = df['code_activity'].nunique()
print(f"Unique medical codes: {unique_codes:,}")

# Check if codes look like CPT codes (5-digit numbers)
df['is_numeric_5digit'] = df['code_activity'].astype(str).str.match(r'^\d{5}$')
cpt_like_codes = df['is_numeric_5digit'].sum()

print(f"5-digit numeric codes (CPT-like): {cpt_like_codes:,} ({cpt_like_codes/len(df)*100:.1f}%)")
print(f"Other code formats: {len(df) - cpt_like_codes:,} ({(len(df) - cpt_like_codes)/len(df)*100:.1f}%)")

# Show most common codes
print("🏆 TOP 10 MOST COMMON MEDICAL CODES")
print("=" * 50)

top_codes = df['code_activity'].value_counts().head(10)
for i, (code, count) in enumerate(top_codes.items(), 1):
    pct = (count / len(df)) * 100
    # Get a sample description
    desc = df[df['code_activity'] == code]['activity_desc'].iloc[0]
    print(f"{i:2d}. {code}: {count:,} times ({pct:.1f}%) - {desc[:50]}...")

# Analyze code patterns
print("🔍 CODE PATTERN ANALYSIS")
print("=" * 50)

# Sample different code formats
sample_codes = df['code_activity'].unique()[:20]
print("Sample codes to understand patterns:")
for code in sample_codes:
    print(f"  • {code}")

# Check code lengths
code_lengths = df['code_activity'].astype(str).str.len()
print(f"\nCode length distribution:")
length_counts = code_lengths.value_counts().sort_index()
for length, count in length_counts.head(10).items():
    pct = (count / len(df)) * 100
    print(f"  {length} characters: {count:,} codes ({pct:.1f}%)")

# Analyze financial columns
financial_cols = ['gross', 'net', 'patient_share', 'payment_amount']
available_financial = [col for col in financial_cols if col in df.columns]

print("💰 FINANCIAL DATA ANALYSIS")
print("=" * 50)
print(f"Available financial columns: {available_financial}")

for col in available_financial:
    data = df[col].dropna()
    if len(data) > 0:
        print(f"\n{col.upper()}:")
        print(f"  Records with data: {len(data):,} ({len(data)/len(df)*100:.1f}%)")
        print(f"  Total amount: ${data.sum():,.2f}")
        print(f"  Average: ${data.mean():.2f}")
        print(f"  Median: ${data.median():.2f}")
        print(f"  Range: ${data.min():.2f} - ${data.max():.2f}")
        print(f"  Zero values: {(data == 0).sum():,}")

# Visualize financial distribution
if 'gross' in df.columns:
    plt.figure(figsize=(12, 5))
    
    # Histogram of gross amounts
    plt.subplot(1, 2, 1)
    gross_data = df['gross'].dropna()
    plt.hist(gross_data, bins=50, edgecolor='black', alpha=0.7)
    plt.title('Distribution of Gross Amounts')
    plt.xlabel('Amount ($)')
    plt.ylabel('Frequency')
    plt.yscale('log')  # Log scale for better visualization
    
    # Box plot
    plt.subplot(1, 2, 2)
    plt.boxplot(gross_data)
    plt.title('Gross Amount Box Plot')
    plt.ylabel('Amount ($)')
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics for cost ranges
    print("\n💵 COST RANGE ANALYSIS")
    print("=" * 30)
    print(f"Free services ($0): {(gross_data == 0).sum():,}")
    print(f"Low cost ($1-$100): {((gross_data > 0) & (gross_data <= 100)).sum():,}")
    print(f"Medium cost ($101-$500): {((gross_data > 100) & (gross_data <= 500)).sum():,}")
    print(f"High cost ($501-$1,000): {((gross_data > 500) & (gross_data <= 1000)).sum():,}")
    print(f"Very high cost ($1,000+): {(gross_data > 1000).sum():,}")

# Analyze healthcare providers
print("👨‍⚕️ HEALTHCARE PROVIDERS ANALYSIS")
print("=" * 50)

if 'clinician' in df.columns:
    unique_clinicians = df['clinician'].nunique()
    records_with_clinician = df['clinician'].notna().sum()
    
    print(f"Unique clinicians: {unique_clinicians:,}")
    print(f"Records with clinician info: {records_with_clinician:,} ({records_with_clinician/len(df)*100:.1f}%)")
    
    # Top clinicians by volume
    top_clinicians = df['clinician'].value_counts().head(10)
    print(f"\nTop 10 most active clinicians:")
    for i, (clinician, count) in enumerate(top_clinicians.items(), 1):
        pct = (count / len(df)) * 100
        print(f"{i:2d}. {clinician}: {count:,} activities ({pct:.1f}%)")

if 'institution_name' in df.columns:
    unique_institutions = df['institution_name'].nunique()
    print(f"\nUnique institutions: {unique_institutions:,}")
    
    # Show all institutions if not too many
    if unique_institutions <= 15:
        institutions = df['institution_name'].value_counts()
        print(f"\nInstitutions:")
        for institution, count in institutions.items():
            pct = (count / len(df)) * 100
            print(f"  • {institution}: {count:,} activities ({pct:.1f}%)")

# Analyze date columns
date_columns = [col for col in df.columns if 'date' in col.lower()]

print("📅 DATE ANALYSIS")
print("=" * 50)
print(f"Date columns found: {date_columns}")

for col in date_columns:
    if col in df.columns:
        print(f"\n{col}:")
        non_null_dates = df[col].notna().sum()
        print(f"  Records with dates: {non_null_dates:,} ({non_null_dates/len(df)*100:.1f}%)")
        
        if non_null_dates > 0:
            # Sample of date formats
            sample_dates = df[col].dropna().head(5).tolist()
            print(f"  Sample dates: {sample_dates}")

# If we have encounter dates, analyze patterns
if 'encounter_start_date' in df.columns and 'encounter_end_date' in df.columns:
    encounter_data = df[['encounter_start_date', 'encounter_end_date']].dropna()
    
    if len(encounter_data) > 0:
        print("🏥 ENCOUNTER PATTERNS")
        print("=" * 50)
        
        # Check for same-day vs multi-day encounters
        same_start_end = (encounter_data['encounter_start_date'] == encounter_data['encounter_end_date']).sum()
        
        print(f"Total encounters with dates: {len(encounter_data):,}")
        print(f"Same-day encounters: {same_start_end:,} ({same_start_end/len(encounter_data)*100:.1f}%)")
        print(f"Multi-day encounters: {len(encounter_data)-same_start_end:,} ({(len(encounter_data)-same_start_end)/len(encounter_data)*100:.1f}%)")

# OMOP mapping assessment - simple and clear
print("🎯 OMOP CDM MAPPING ASSESSMENT")
print("=" * 50)

print("The OMOP Common Data Model has several key tables. Let's see how our data fits:")
print()

# PERSON table potential
print("👥 PERSON Table:")
print("   Purpose: Store patient demographics")
print("   Our data: ✅ aio_patient_id (can be person_id)")
print("   Missing: ❌ birth_date, gender, race (common in anonymized data)")
print("   Status: Basic implementation possible")
print()

# VISIT_OCCURRENCE potential  
print("🏥 VISIT_OCCURRENCE Table:")
print("   Purpose: Store healthcare encounters")
print("   Our data: ✅ case (visit_occurrence_id), encounter dates")
if 'case_type' in df.columns:
    print("   Our data: ✅ case_type (visit_type)")
print("   Status: Excellent mapping potential")
print()

# PROCEDURE_OCCURRENCE potential
print("🩺 PROCEDURE_OCCURRENCE Table:")
print("   Purpose: Store medical procedures and services")
print("   Our data: ✅ code_activity (procedure codes)")
print("   Our data: ✅ activity_desc (procedure descriptions)")
print("   Our data: ✅ start_activity_date (procedure_date)")
print(f"   Code quality: {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes")
print("   Status: Strong mapping potential")
print()

# COST table potential
print("💰 COST Table:")
print("   Purpose: Store financial information")
if 'gross' in df.columns:
    print("   Our data: ✅ gross (total_charge)")
if 'payment_amount' in df.columns:
    print("   Our data: ✅ payment_amount (total_paid)")
if 'patient_share' in df.columns:
    print("   Our data: ✅ patient_share (patient_copay)")
print("   Status: Excellent financial data available")
print()

# PROVIDER table potential
print("👨‍⚕️ PROVIDER Table:")
print("   Purpose: Store healthcare provider information")
if 'clinician' in df.columns:
    print("   Our data: ✅ clinician (provider_name)")
print("   Missing: ❌ provider specialty, credentials")
print("   Status: Basic provider tracking possible")

# Summary of data readiness for OMOP
print("📊 OMOP IMPLEMENTATION READINESS SUMMARY")
print("=" * 50)

readiness_scores = {
    'PERSON': 60,  # Basic patient ID only
    'VISIT_OCCURRENCE': 85,  # Good encounter data
    'PROCEDURE_OCCURRENCE': 80,  # Good procedure codes and dates
    'COST': 90,  # Excellent financial data
    'PROVIDER': 65,  # Basic provider info
    'DRUG_EXPOSURE': 10,  # No medication data visible
    'CONDITION_OCCURRENCE': 20,  # No diagnosis codes visible
}

print("Table readiness scores (0-100%):")
for table, score in readiness_scores.items():
    if score >= 70:
        status = "🟢 HIGH"
    elif score >= 50:
        status = "🟡 MEDIUM" 
    else:
        status = "🔴 LOW"
    print(f"  {table}: {score}% {status}")

print(f"\nOverall assessment:")
avg_score = sum(readiness_scores.values()) / len(readiness_scores)
print(f"  Average readiness: {avg_score:.0f}%")
print(f"  Primary strength: Financial and procedure data")
print(f"  Main limitation: Missing clinical details (diagnoses, medications)")
print(f"  Recommendation: Focus on procedure and cost analysis initially")

print("🎯 KEY INSIGHTS")
print("=" * 50)

print("📈 Data Volume:")
print(f"  • {unique_patients:,} patients with {total_activities:,} healthcare activities")
print(f"  • {unique_cases:,} unique encounters/cases")
print(f"  • Average {total_activities/unique_patients:.1f} activities per patient")

print(f"\n🩺 Medical Procedures:")
print(f"  • {unique_codes:,} unique medical codes")
print(f"  • {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes")
print(f"  • {(len(df) - cpt_like_codes)/len(df)*100:.1f}% use local/regional coding")

if 'gross' in df.columns:
    total_value = df['gross'].sum()
    print(f"\n💰 Financial Impact:")
    print(f"  • Total healthcare value: ${total_value:,.2f}")
    print(f"  • Average cost per activity: ${df['gross'].mean():.2f}")

print(f"\n🏥 Healthcare Delivery:")
if 'clinician' in df.columns:
    print(f"  • {unique_clinicians:,} healthcare providers")
if 'institution_name' in df.columns:
    print(f"  • {unique_institutions:,} healthcare institutions")

print(f"\n🎯 OMOP Transformation Priority:")
print(f"  1. Start with PROCEDURE_OCCURRENCE (strong code data)")
print(f"  2. Implement COST table (excellent financial data)")
print(f"  3. Add VISIT_OCCURRENCE (good encounter structure)")
print(f"  4. Create basic PERSON records (patient IDs only)")
print(f"  5. Later: enhance with external code mappings")