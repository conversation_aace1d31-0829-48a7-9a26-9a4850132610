{"cells": [{"cell_type": "markdown", "id": "22f91bc4", "metadata": {}, "source": ["# Abu Dhabi Claims Dataset - Exploratory Data Analysis\n", "\n", "## Overview\n", "Analysis of the Abu Dhabi healthcare claims CSV\n", "- **Data structure and quality**\n", "- **Patient and encounter patterns** \n", "- **Medical codes and procedures**\n", "- **Financial information**\n", "- **Potential for OMOP CDM mapping**"]}, {"cell_type": "code", "execution_count": 47, "id": "aabc9593", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "plt.style.use('default')\n", "sns.set_palette('husl')"]}, {"cell_type": "markdown", "id": "23ba3dd1", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and First Look at the Data"]}, {"cell_type": "code", "execution_count": 48, "id": "b3bf1877", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 DATASET OVERVIEW\n", "==================================================\n", "Total records: 4,999\n", "Total columns: 54\n"]}], "source": ["# Load the dataset\n", "file_path = '../../../../../data/real_test_datasets/claim_anonymized.csv'\n", "df = pd.read_csv(file_path)\n", "\n", "print(\"📊 DATASET OVERVIEW\")\n", "print(\"=\" * 50)\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Total columns: {df.shape[1]}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "d4ef38ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COLUMN INFORMATION\n", "==================================================\n", "Column names:\n", " 1. provider_id (object)\n", " 2. institution_name (object)\n", " 3. case_type (object)\n", " 4. claim_id (int64)\n", " 5. claim_net (float64)\n", " 6. unique_id (object)\n", " 7. case (int64)\n", " 8. insurance_plan_id (int64)\n", " 9. plan_name (object)\n", "10. network_name (object)\n", "11. payer_id (object)\n", "12. payer_id_desc (object)\n", "13. id_payer (object)\n", "14. denial_code (object)\n", "15. code_activity (object)\n", "16. activity_desc (object)\n", "17. activity_id (int64)\n", "18. reference_activity (object)\n", "19. start_activity_date (object)\n", "20. type_activity (int64)\n", "21. act_type_desc (object)\n", "22. activity_quantity (float64)\n", "23. mapping_status (object)\n", "24. claim_mapping_status (object)\n", "25. gross (float64)\n", "26. patient_share (float64)\n", "27. net (float64)\n", "28. payment_amount (float64)\n", "29. rejected_amount (float64)\n", "30. resub_net (float64)\n", "31. clinician (object)\n", "32. clinician_name (object)\n", "33. resub_date (object)\n", "34. remittance_date (object)\n", "35. ra_aging (int64)\n", "36. resub_aging (int64)\n", "37. claim_status_desc (object)\n", "38. resub_type_desc (object)\n", "39. encounter_start_type (int64)\n", "40. encounter_start_type_desc (object)\n", "41. encounter_start_date (object)\n", "42. encounter_end_date (object)\n", "43. encounter_end_type (float64)\n", "44. encounter_end_type_desc (object)\n", "45. receiver_id (object)\n", "46. receiver_id_desc (object)\n", "47. prior_authorization (object)\n", "48. submission_date (object)\n", "49. processing_status (object)\n", "50. accepted_type (object)\n", "51. accepted_type_reason_items (object)\n", "52. reconciliation_claim_tag (object)\n", "53. year_encounter_end_date (int64)\n", "54. aio_patient_id (object)\n"]}], "source": ["# Check column names and data types\n", "print(\"📋 COLUMN INFORMATION\")\n", "print(\"=\" * 50)\n", "print(f\"Column names:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col} ({df[col].dtype})\")"]}, {"cell_type": "code", "execution_count": 22, "id": "674ae3a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👀 SAMPLE DATA (First 3 rows)\n", "==================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>provider_id</th>\n", "      <td>MF4252</td>\n", "      <td>MF4252</td>\n", "      <td>MF4252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>institution_name</th>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>case_type</th>\n", "      <td>Outpatient Case</td>\n", "      <td>Outpatient Case</td>\n", "      <td>Outpatient Case</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_id</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_net</th>\n", "      <td>221.0</td>\n", "      <td>221.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique_id</th>\n", "      <td>MF4252**********</td>\n", "      <td>MF4252**********</td>\n", "      <td>MF4252**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>case</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance_plan_id</th>\n", "      <td>700000</td>\n", "      <td>700000</td>\n", "      <td>700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>plan_name</th>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>network_name</th>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>ALDAR-COMP 3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payer_id</th>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payer_id_desc</th>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>id_payer</th>\n", "      <td>2.12E+11</td>\n", "      <td>2.12E+11</td>\n", "      <td>2.12E+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>denial_code</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>code_activity</th>\n", "      <td>87880</td>\n", "      <td>99203</td>\n", "      <td>99203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_desc</th>\n", "      <td>Group A Streptococcus Antigen, Throat Swab</td>\n", "      <td>Office or other outpatient visit for the evalu...</td>\n", "      <td>Office or other outpatient visit for the evalu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_id</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>reference_activity</th>\n", "      <td>154527198</td>\n", "      <td>154682709</td>\n", "      <td>154658090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>start_activity_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>type_activity</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>act_type_desc</th>\n", "      <td>CPT</td>\n", "      <td>CPT</td>\n", "      <td>CPT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_quantity</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mapping_status</th>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_mapping_status</th>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross</th>\n", "      <td>43.0</td>\n", "      <td>142.0</td>\n", "      <td>142.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>patient_share</th>\n", "      <td>0.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net</th>\n", "      <td>43.0</td>\n", "      <td>92.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payment_amount</th>\n", "      <td>43.0</td>\n", "      <td>92.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rejected_amount</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_net</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>clinician</th>\n", "      <td>GD11650</td>\n", "      <td>GD11650</td>\n", "      <td>GD25783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>clinician_name</th>\n", "      <td>PRASANNA SHETTY</td>\n", "      <td>PRASANNA SHETTY</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_date</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>remittance_date</th>\n", "      <td>07/10/2023</td>\n", "      <td>07/10/2023</td>\n", "      <td>28/07/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ra_aging</th>\n", "      <td>110</td>\n", "      <td>110</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_aging</th>\n", "      <td>564</td>\n", "      <td>564</td>\n", "      <td>635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_status_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_type_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_type</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_type_desc</th>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_type</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_type_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>receiver_id</th>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>receiver_id_desc</th>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prior_authorization</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>submission_date</th>\n", "      <td>19/06/2023</td>\n", "      <td>19/06/2023</td>\n", "      <td>19/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processing_status</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accepted_type</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accepted_type_reason_items</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>reconciliation_claim_tag</th>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year_encounter_end_date</th>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aio_patient_id</th>\n", "      <td>AIO00001</td>\n", "      <td>AIO00001</td>\n", "      <td>AIO00002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>is_numeric_5digit</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                     0  \\\n", "provider_id                                                     MF4252   \n", "institution_name                                                  BDSC   \n", "case_type                                              Outpatient Case   \n", "claim_id                                                    **********   \n", "claim_net                                                        221.0   \n", "unique_id                                             MF4252**********   \n", "case                                                        **********   \n", "insurance_plan_id                                               700000   \n", "plan_name                                      COMPREHENSIVE 3 - ALDAR   \n", "network_name                                              ALDAR-COMP 3   \n", "payer_id                                                          A001   \n", "payer_id_desc                                          Daman Insurance   \n", "id_payer                                                      2.12E+11   \n", "denial_code                                                        NaN   \n", "code_activity                                                    87880   \n", "activity_desc               Group A Streptococcus Antigen, Throat Swab   \n", "activity_id                                                 **********   \n", "reference_activity                                           154527198   \n", "start_activity_date                                         16/06/2023   \n", "type_activity                                                        3   \n", "act_type_desc                                                      CPT   \n", "activity_quantity                                                  1.0   \n", "mapping_status                                              Fully Paid   \n", "claim_mapping_status                                        Fully Paid   \n", "gross                                                             43.0   \n", "patient_share                                                      0.0   \n", "net                                                               43.0   \n", "payment_amount                                                    43.0   \n", "rejected_amount                                                    0.0   \n", "resub_net                                                          0.0   \n", "clinician                                                      GD11650   \n", "clinician_name                                         PRASANNA SHETTY   \n", "resub_date                                                         NaN   \n", "remittance_date                                             07/10/2023   \n", "ra_aging                                                           110   \n", "resub_aging                                                        564   \n", "claim_status_desc                                                  NaN   \n", "resub_type_desc                                                    NaN   \n", "encounter_start_type                                                 1   \n", "encounter_start_type_desc     Elective, i.e., an Encounter is schedule   \n", "encounter_start_date                                        16/06/2023   \n", "encounter_end_date                                          16/06/2023   \n", "encounter_end_type                                                 NaN   \n", "encounter_end_type_desc                                            NaN   \n", "receiver_id                                                       A001   \n", "receiver_id_desc                                       Daman Insurance   \n", "prior_authorization                                                NaN   \n", "submission_date                                             19/06/2023   \n", "processing_status                                                  NaN   \n", "accepted_type                                                      NaN   \n", "accepted_type_reason_items                                         NaN   \n", "reconciliation_claim_tag                                            No   \n", "year_encounter_end_date                                           2023   \n", "aio_patient_id                                                AIO00001   \n", "is_numeric_5digit                                                 True   \n", "\n", "                                                                            1  \\\n", "provider_id                                                            MF4252   \n", "institution_name                                                         BDSC   \n", "case_type                                                     Outpatient Case   \n", "claim_id                                                           **********   \n", "claim_net                                                               221.0   \n", "unique_id                                                    MF4252**********   \n", "case                                                               **********   \n", "insurance_plan_id                                                      700000   \n", "plan_name                                             COMPREHENSIVE 3 - ALDAR   \n", "network_name                                                     ALDAR-COMP 3   \n", "payer_id                                                                 A001   \n", "payer_id_desc                                                 Daman Insurance   \n", "id_payer                                                             2.12E+11   \n", "denial_code                                                               NaN   \n", "code_activity                                                           99203   \n", "activity_desc               Office or other outpatient visit for the evalu...   \n", "activity_id                                                        **********   \n", "reference_activity                                                  154682709   \n", "start_activity_date                                                16/06/2023   \n", "type_activity                                                               3   \n", "act_type_desc                                                             CPT   \n", "activity_quantity                                                         1.0   \n", "mapping_status                                                     Fully Paid   \n", "claim_mapping_status                                               Fully Paid   \n", "gross                                                                   142.0   \n", "patient_share                                                            50.0   \n", "net                                                                      92.0   \n", "payment_amount                                                           92.0   \n", "rejected_amount                                                           0.0   \n", "resub_net                                                                 0.0   \n", "clinician                                                             GD11650   \n", "clinician_name                                                PRASANNA SHETTY   \n", "resub_date                                                                NaN   \n", "remittance_date                                                    07/10/2023   \n", "ra_aging                                                                  110   \n", "resub_aging                                                               564   \n", "claim_status_desc                                                         NaN   \n", "resub_type_desc                                                           NaN   \n", "encounter_start_type                                                        1   \n", "encounter_start_type_desc            Elective, i.e., an Encounter is schedule   \n", "encounter_start_date                                               16/06/2023   \n", "encounter_end_date                                                 16/06/2023   \n", "encounter_end_type                                                        NaN   \n", "encounter_end_type_desc                                                   NaN   \n", "receiver_id                                                              A001   \n", "receiver_id_desc                                              Daman Insurance   \n", "prior_authorization                                                       NaN   \n", "submission_date                                                    19/06/2023   \n", "processing_status                                                         NaN   \n", "accepted_type                                                             NaN   \n", "accepted_type_reason_items                                                NaN   \n", "reconciliation_claim_tag                                                   No   \n", "year_encounter_end_date                                                  2023   \n", "aio_patient_id                                                       AIO00001   \n", "is_numeric_5digit                                                        True   \n", "\n", "                                                                            2  \n", "provider_id                                                            MF4252  \n", "institution_name                                                         BDSC  \n", "case_type                                                     Outpatient Case  \n", "claim_id                                                           **********  \n", "claim_net                                                                92.0  \n", "unique_id                                                    MF4252**********  \n", "case                                                               **********  \n", "insurance_plan_id                                                      700000  \n", "plan_name                                             COMPREHENSIVE 3 - ALDAR  \n", "network_name                                                     ALDAR-COMP 3  \n", "payer_id                                                                 A001  \n", "payer_id_desc                                                 Daman Insurance  \n", "id_payer                                                             2.12E+11  \n", "denial_code                                                               NaN  \n", "code_activity                                                           99203  \n", "activity_desc               Office or other outpatient visit for the evalu...  \n", "activity_id                                                        **********  \n", "reference_activity                                                  154658090  \n", "start_activity_date                                                16/06/2023  \n", "type_activity                                                               3  \n", "act_type_desc                                                             CPT  \n", "activity_quantity                                                         1.0  \n", "mapping_status                                                     Fully Paid  \n", "claim_mapping_status                                               Fully Paid  \n", "gross                                                                   142.0  \n", "patient_share                                                            50.0  \n", "net                                                                      92.0  \n", "payment_amount                                                           92.0  \n", "rejected_amount                                                           0.0  \n", "resub_net                                                                 0.0  \n", "clinician                                                             GD25783  \n", "clinician_name                                                   <PERSON><PERSON>  \n", "resub_date                                                                NaN  \n", "remittance_date                                                    28/07/2023  \n", "ra_aging                                                                   39  \n", "resub_aging                                                               635  \n", "claim_status_desc                                                         NaN  \n", "resub_type_desc                                                           NaN  \n", "encounter_start_type                                                        1  \n", "encounter_start_type_desc            Elective, i.e., an Encounter is schedule  \n", "encounter_start_date                                               16/06/2023  \n", "encounter_end_date                                                 16/06/2023  \n", "encounter_end_type                                                        NaN  \n", "encounter_end_type_desc                                                   NaN  \n", "receiver_id                                                              A001  \n", "receiver_id_desc                                              Daman Insurance  \n", "prior_authorization                                                       NaN  \n", "submission_date                                                    19/06/2023  \n", "processing_status                                                         NaN  \n", "accepted_type                                                             NaN  \n", "accepted_type_reason_items                                                NaN  \n", "reconciliation_claim_tag                                                   No  \n", "year_encounter_end_date                                                  2023  \n", "aio_patient_id                                                       AIO00002  \n", "is_numeric_5digit                                                        True  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Quick sample of the data\n", "print(\"👀 SAMPLE DATA (First 3 rows)\")\n", "print(\"=\" * 50)\n", "display(df.head(3).T)"]}, {"cell_type": "markdown", "id": "7d5d5490", "metadata": {}, "source": ["## 2. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": 24, "id": "65d1af9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 MISSING DATA ANALYSIS\n", "==================================================\n", "Columns with missing data:\n", "  • accepted_type_reason_items: 4,912.0 (98.3%)\n", "  • resub_type_desc: 4,651.0 (93.0%)\n", "  • claim_status_desc: 4,292.0 (85.9%)\n", "  • accepted_type: 4,281.0 (85.6%)\n", "  • processing_status: 4,281.0 (85.6%)\n", "  • denial_code: 4,122.0 (82.5%)\n", "  • encounter_end_type_desc: 4,117.0 (82.4%)\n", "  • encounter_end_type: 4,117.0 (82.4%)\n", "  • resub_date: 3,853.0 (77.1%)\n", "  • prior_authorization: 3,294.0 (65.9%)\n", "  • activity_desc: 81.0 (1.6%)\n", "  • clinician_name: 12.0 (0.2%)\n", "  • id_payer: 6.0 (0.1%)\n", "  • remittance_date: 6.0 (0.1%)\n"]}], "source": ["# Check for missing values\n", "print(\"🔍 MISSING DATA ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "missing_data = df.isnull().sum()\n", "missing_pct = (missing_data / len(df)) * 100\n", "\n", "missing_summary = pd.DataFrame({\n", "    'Missing_Count': missing_data,\n", "    'Missing_Percentage': missing_pct\n", "}).sort_values('Missing_Count', ascending=False)\n", "\n", "# Show only columns with missing data\n", "missing_with_nulls = missing_summary[missing_summary['Missing_Count'] > 0]\n", "\n", "if len(missing_with_nulls) > 0:\n", "    print(\"Columns with missing data:\")\n", "    for col, row in missing_with_nulls.iterrows():\n", "        print(f\"  • {col}: {row['Missing_Count']:,} ({row['Missing_Percentage']:.1f}%)\")\n", "else:\n", "    print(\"✅ No missing data found!\")"]}, {"cell_type": "code", "execution_count": 25, "id": "0a5aee34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 DUPLICATE ANALYSIS\n", "==================================================\n", "Exact duplicate rows: 0\n", "Duplicate activity IDs: 0\n", "✅ All activity IDs are unique\n"]}], "source": ["# Check for duplicate records\n", "print(\"🔄 DUPLICATE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "total_duplicates = df.duplicated().sum()\n", "print(f\"Exact duplicate rows: {total_duplicates:,}\")\n", "\n", "# Check for duplicate activity IDs (should be unique)\n", "if 'activity_id' in df.columns:\n", "    duplicate_activity_ids = df['activity_id'].duplicated().sum()\n", "    print(f\"Duplicate activity IDs: {duplicate_activity_ids:,}\")\n", "    \n", "    if duplicate_activity_ids == 0:\n", "        print(\"✅ All activity IDs are unique\")\n", "    else:\n", "        print(\"⚠️  Some activity IDs are duplicated\")"]}, {"cell_type": "markdown", "id": "996ffe21", "metadata": {}, "source": ["## 3. Patient and Case Analysis\n", "Understanding the core entities in our data"]}, {"cell_type": "code", "execution_count": 38, "id": "65c47ceb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔢 UNIQUE CASES\n", "==================================================\n", "Total unique cases: 1,461\n", "Unique cases in 'case' column:\n", "[********** ********** ********** ...   17587575   17581149   17582237]\n"]}], "source": ["# number o of unique cases\n", "print(\"🔢 UNIQUE CASES\")\n", "print(\"=\" * 50)\n", "unique_cases = df['case'].nunique()\n", "print(f\"Total unique cases: {unique_cases:,}\")\n", "# Display unique cases\n", "print(\"Unique cases in 'case' column:\")\n", "print(df['case'].unique())"]}, {"cell_type": "code", "execution_count": 7, "id": "2acdff95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👥 PATIENT ANALYSIS\n", "==================================================\n", "Unique patients: 596\n", "Unique cases/encounters: 1,461\n", "Total activities/procedures: 4,999\n", "\n", "Average activities per patient: 8.4\n", "Average activities per case: 3.4\n", "Average cases per patient: 2.5\n"]}], "source": ["# Analyze patients and cases\n", "print(\"👥 PATIENT ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "unique_patients = df['aio_patient_id'].nunique()\n", "unique_cases = df['case'].nunique() \n", "total_activities = len(df)\n", "\n", "print(f\"Unique patients: {unique_patients:,}\")\n", "print(f\"Unique cases/encounters: {unique_cases:,}\")\n", "print(f\"Total activities/procedures: {total_activities:,}\")\n", "print()\n", "print(f\"Average activities per patient: {total_activities / unique_patients:.1f}\")\n", "print(f\"Average activities per case: {total_activities / unique_cases:.1f}\")\n", "print(f\"Average cases per patient: {unique_cases / unique_patients:.1f}\")"]}, {"cell_type": "code", "execution_count": 46, "id": "46a9f769", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 ACTIVITIES PER PATIENT DISTRIBUTION\n", "==================================================\n", "Minimum activities: 1\n", "Maximum activities: 150\n", "Median activities: 3\n", "Mean activities: 8.4\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1IAAAE6CAYAAAAcHmMZAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAASPNJREFUeJzt3Xl0FFX6//FPZ4eYAAlZCGBARFT2xYWg7MuXVQcdQBBBYMQRkQgoOIwSmJEAKqJh0xEJKoujAuICEhRQDMoaBETc2JTEIIQEEkhC+v7+4J<PERSON><PERSON>ykoJsO+H6d0+fQt27d+9TT3SRPblW1zRhjBAAAAAAoMy9PBwAAAAAAVxoKKQAAAACwiEIKAAAAACyikAIAAAAAiyikAAAAAMAiCikAAAAAsIhCCgAAAAAsopACAAAAAIsopAAAAADAIgopAFe8xMRE2Ww2xyMgIECRkZFq166d4uPjlZ6eXmSfuLg42Ww2S/Pk5OQoLi5O69evt7RfcXPVqlVLPXr0sDTOhSxevFgzZ84sdpvNZlNcXJxL53O1Tz/9VC1atFBgYKBsNptWrFhxwX127dolm80mX19fpaamXvTcU6ZMKXa+9evXy2azWX7NJWnw4MGqVauW2+e5khUed+HD29tbERER+utf/6q9e/daHs/Tn4GPP/643H/OALgOhRSAq8aCBQu0adMmJSUlafbs2WrSpImmTZumm266SWvXrnXqO2zYMG3atMnS+Dk5OZo0aZLlX3YvZq6LUdovkZs2bdKwYcPcHsPFMsaoT58+8vX11cqVK7Vp0ya1adPmgvu99tprkqSzZ8/qjTfeuOj5SypwmjVrpk2bNqlZs2aWx3z66ae1fPlyt89zNZgyZYo2bdqkdevWady4cUpKSlKrVq3066+/WhrH05+Bjz/+WJMmTXLrHADKDx9PBwAArtKgQQO1aNHC8fyee+7R448/rjvuuEO9e/fWDz/8oIiICElSjRo1VKNGDbfGk5OTo4oVK16WuS7k9ttv9+j8F3LkyBEdP35cf/nLX9ShQ4cy7ZObm6tFixapcePG+v333/X6669r3LhxLo0rODj4onNXp06dyzJPeVf4OShN3bp1HcffunVrVa5cWUOHDlViYqImTJjgkjiu1vwC8BxWpABc1a699lq98MILOnnypF555RVHe3Gn23322Wdq27atQkNDVaFCBV177bW65557lJOTowMHDigsLEySNGnSJMepSIMHD3Yab/v27br33ntVpUoVxy/SpZ1GuHz5cjVq1EgBAQG67rrr9PLLLzttLzxt8cCBA07t558K1rZtW3300Uc6ePCg06lShYo7rWn37t266667VKVKFQUEBKhJkyZauHBhsfMsWbJEEyZMUFRUlIKDg9WxY0ft27ev5MT/wcaNG9WhQwcFBQWpYsWKiomJ0UcffeTYHhcX5yg0x40bJ5vNVuSUuOKsWLFCx44d07BhwzRo0CB9//332rhxY5F+ubm5mjx5sm666SYFBAQoNDRU7dq1U3JysiM32dnZWrhwoSNvbdu2dTr+wjzPnDlTNptNP/74Y5F5xo0bJz8/P/3++++Sip7aZ2WeQlu3blWvXr0UEhKigIAANW3aVP/973+d+uTk5Gjs2LGqXbu2AgICFBISohYtWmjJkiWl5q/wvZWUlKQHH3xQISEhCgwMVM+ePfXzzz8X6b927Vp16NBBwcHBqlixolq1aqVPP/3UqU9pnwMrCouegwcPSpJmz56t1q1bKzw8XIGBgWrYsKGmT5+u/Px8xz4X8xlIS0vT8OHDVaNGDfn5+al27dqaNGmSzp496+hz4MAB2Ww2Pf/885oxY4Zq166ta665Ri1bttRXX33l6Dd48GDNnj3bMVfh4/zPLoCrB4UUgKtet27d5O3trc8//7zEPgcOHFD37t3l5+en119/XatXr9bUqVMVGBiovLw8VatWTatXr5YkDR06VJs2bdKmTZv09NNPO43Tu3dvXX/99XrnnXc0b968UuNKSUlRbGysHn/8cS1fvlwxMTEaNWqUnn/+ecvHOGfOHLVq1UqRkZGO2Eo7nXDfvn2KiYnRnj179PLLL2vZsmW6+eabNXjwYE2fPr1I/3/84x86ePCgXnvtNb366qv64Ycf1LNnTxUUFJQa14YNG9S+fXtlZmZq/vz5WrJkiYKCgtSzZ0+9/fbbks6d+rhs2TJJ0siRI7Vp06Yip8QVZ/78+fL399eAAQM0ZMgQ2Ww2zZ8/36nP2bNn1bVrV/3rX/9Sjx49tHz5ciUmJiomJkaHDh2SdO6UrwoVKqhbt26OvM2ZM6fYOe+//375+fkpMTHRqb2goEBvvfWWevbsqapVqxa7r5V5JGndunVq1aqVTpw4oXnz5un9999XkyZN1LdvX6f5R48erblz5+qxxx7T6tWr9eabb+qvf/2rjh07dsEcSufez15eXo7T4jZv3qy2bdvqxIkTjj5vvfWWOnfurODgYC1cuFD//e9/FRISoi5duhQppiRrn4PiFBaqhX+8+Omnn9S/f3+9+eab+vDDDzV06FA999xzGj58uGMfq5+BtLQ03Xrrrfrkk0/0zDPPaNWqVRo6dKji4+P1t7/9rUj/2bNnKykpSTNnztSiRYuUnZ2tbt26KTMzU9K5UznvvfdeSXKav1q1apaPH8AVwgDAFW7BggVGktmyZUuJfSIiIsxNN93keD5x4kTzx/8C3333XSPJpKSklDjG0aNHjSQzceLEItsKx3vmmWdK3PZH0dHRxmazFZmvU6dOJjg42GRnZzsd2/79+536rVu3zkgy69atc7R1797dREdHFxv7+XH369fP+Pv7m0OHDjn169q1q6lYsaI5ceKE0zzdunVz6vff//7XSDKbNm0qdr5Ct99+uwkPDzcnT550tJ09e9Y0aNDA1KhRw9jtdmOMMfv37zeSzHPPPVfqeIUOHDhgvLy8TL9+/Rxtbdq0MYGBgSYrK8vR9sYbbxhJ5j//+U+p4wUGBppBgwYVaS8uz7179zY1atQwBQUFjraPP/7YSDIffPCBo23QoEFFXg8r89x4442madOmJj8/36lvjx49TLVq1RzzN2jQwNx9992lHl9xCt9bf/nLX5zav/zySyPJ/Pvf/zbGGJOdnW1CQkJMz549nfoVFBSYxo0bm1tvvdXRVtrnoDiFx/3222+b/Px8k5OTYz7//HNz/fXXG29vb7Nz584i+xQUFJj8/HzzxhtvGG9vb3P8+HHHNiufgeHDh5trrrnGHDx40Knf888/bySZPXv2GGP+995s2LChOXv2rKPf5s2bjSSzZMkSR9uIESOKfNYBXL1YkQLwp2CMKXV7kyZN5Ofnp4ceekgLFy4s9tSmsrjnnnvK3Ld+/fpq3LixU1v//v2VlZWl7du3X9T8ZfXZZ5+pQ4cOqlmzplP74MGDlZOTU+Qv+b169XJ63qhRI0n/O/WqONnZ2fr6669177336pprrnG0e3t7a+DAgfrll1/KfHrg+RYsWCC73a4hQ4Y42oYMGaLs7GzHSpckrVq1SgEBAU79LtWDDz6oX375xekGJgsWLFBkZKS6du3qkjl+/PFHfffddxowYICkcytrhY9u3bopNTXVkbtbb71Vq1at0vjx47V+/XqdPn3a0lyFcxSKiYlRdHS01q1bJ0lKTk7W8ePHNWjQIKc47Ha7/u///k9btmxRdna20xhWPgeS1LdvX/n6+qpixYpq3bq1CgoK9O677zreZzt27FCvXr0UGhoqb29v+fr66oEHHlBBQYG+//57S3MV+vDDD9WuXTtFRUU5HVfha7hhwwan/t27d5e3t7fjeVk+AwCubhRSAK562dnZOnbsmKKiokrsU6dOHa1du1bh4eEaMWKE6tSpozp16uill16yNJeV03giIyNLbCvraVkX69ixY8XGWpij8+cPDQ11eu7v7y9Jpf7SnpGRIWOMpXnKwm63KzExUVFRUWrevLlOnDihEydOqGPHjgoMDHQ6ve/o0aOKioqSl5frftx17dpV1apV04IFCySdO86VK1fqgQcecPpF+1L89ttvkqSxY8fK19fX6fHII49IkuNarJdfflnjxo3TihUr1K5dO4WEhOjuu+/WDz/8UKa5SnofFr42hbHce++9RWKZNm2ajDE6fvy40/5WT2ebNm2atmzZou3bt+vQoUP6+eefdffdd0uSDh06pDvvvFO//vqrXnrpJX3xxRfasmWL43okq4Vjod9++00ffPBBkWOqX7++pP/lt9DFfAYAXN24ax+Aq95HH32kgoICx4X9Jbnzzjt15513qqCgQFu3blVCQoJiY2MVERGhfv36lWkuK99NlZaWVmJb4S9tAQEBks7dMOGPzv8lz6rQ0NBiv3fpyJEjklTidT5WVKlSRV5eXi6fZ+3atY5VgPN/uZWkr776St9++61uvvlmhYWFaePGjbLb7S4rpgpX1F5++WWdOHFCixcvVm5urh588EGXjC/9Ly9PPfWUevfuXWyfevXqSZICAwM1adIkTZo0Sb/99ptjdapnz5767rvvLjhXSe/D66+/3imWhISEEu98V3g3zEJWv6Ptuuuuc7rj5h+tWLFC2dnZWrZsmaKjox3tKSkpluY4X9WqVdWoUSM9++yzxW4v7Q8vACCxIgXgKnfo0CGNHTtWlSpVcrowvTTe3t667bbbHH/xLjzNztV/gd6zZ4927tzp1LZ48WIFBQU5vk+o8K5v33zzjVO/lStXFhnP39+/zLF16NBBn332maOgKfTGG2+oYsWKLrlVdGBgoG677TYtW7bMKS673a633npLNWrU0A033GB53Pnz58vLy0srVqzQunXrnB5vvvmmJOn111+XdG716MyZM0VuDnE+K7mTzp3ed+bMGS1ZskSJiYlq2bKlbrzxxgvuV9Z56tWrp7p162rnzp1q0aJFsY+goKAi+0VERGjw4MG67777tG/fPuXk5FxwrkWLFjk9T05O1sGDBx1/eGjVqpUqV66sb7/9tsRY/Pz8LjjPxSosygo/f9K5U3X/85//FOlr5XXs0aOHdu/erTp16hR7TBdTSLFKBfy5sCIF4Kqxe/dux3UO6enp+uKLL7RgwQJ5e3tr+fLljjuAFWfevHn67LPP1L17d1177bU6c+aM45fxjh07SpKCgoIUHR2t999/Xx06dFBISIiqVq1aplt1FycqKkq9evVSXFycqlWrprfeektJSUmaNm2a43t3brnlFtWrV09jx47V2bNnVaVKFS1fvrzY23w3bNhQy5Yt09y5c9W8eXN5eXmV+Ff+iRMnOq4ReeaZZxQSEqJFixbpo48+0vTp01WpUqWLOqbzxcfHq1OnTmrXrp3Gjh0rPz8/zZkzR7t379aSJUssr1wcO3ZM77//vrp06aK77rqr2D4vvvii3njjDcXHx+u+++7TggUL9PDDD2vfvn1q166d7Ha7vv76a910002OlcaGDRtq/fr1+uCDD1StWjUFBQU5VnyKc+ONN6ply5aKj4/X4cOH9eqrr5YpfivzvPLKK+ratau6dOmiwYMHq3r16jp+/Lj27t2r7du365133pEk3XbbberRo4caNWqkKlWqaO/evXrzzTfVsmXLC35/k3TuFuvDhg3TX//6Vx0+fFgTJkxQ9erVHacQXnPNNUpISNCgQYN0/Phx3XvvvQoPD9fRo0e1c+dOHT16VHPnzi3T8V+MTp06yc/PT/fdd5+efPJJnTlzRnPnzlVGRkaRvlY+A5MnT1ZSUpJiYmL02GOPqV69ejpz5owOHDigjz/+WPPmzbP8/W8NGzaUdO5Uxa5du8rb21uNGjVya6EJwIM8e68LALh0hXcfK3z4+fmZ8PBw06ZNGzNlyhSTnp5eZJ/z76S3adMm85e//MVER0cbf39/Exoaatq0aWNWrlzptN/atWtN06ZNjb+/v5HkuANb4XhHjx694FzGnLtrX/fu3c27775r6tevb/z8/EytWrXMjBkziuz//fffm86dO5vg4GATFhZmRo4caT766KMid3k7fvy4uffee03lypWNzWZzmlPF3G1w165dpmfPnqZSpUrGz8/PNG7c2CxYsMCpT+Fd1d555x2n9sI7mZ3fvzhffPGFad++vQkMDDQVKlQwt99+u9Pd7f443oXu2jdz5kwjyaxYsaLEPvPmzTOSzHvvvWeMMeb06dPmmWeeMXXr1jV+fn4mNDTUtG/f3iQnJzv2SUlJMa1atTIVK1Y0kkybNm2cjv+PeS706quvGkmmQoUKJjMzs8j24u7aZ3WenTt3mj59+pjw8HDj6+trIiMjTfv27c28efMcfcaPH29atGhhqlSpYvz9/c11111nHn/8cfP777+Xksn/fW7WrFljBg4caCpXrmwqVKhgunXrZn744Yci/Tds2GC6d+9uQkJCjK+vr6levbrp3r2703ujtM9BcUp6f53vgw8+MI0bNzYBAQGmevXq5oknnjCrVq265M/A0aNHzWOPPWZq165tfH19TUhIiGnevLmZMGGCOXXqlDGm9Pfm+WPm5uaaYcOGmbCwMMf8599xE8DVw2bMBW5lBQAArjqJiYl68MEHtWXLlhJXbQAAJeMaKQAAAACwiEIKAAAAACzi1D4AAAAAsIgVKQAAAACwiEIKAAAAACyikAIAAAAAi/hCXkl2u11HjhxRUFCQ5S+HBAAAAHD1MMbo5MmTioqKkpdXyetOFFKSjhw5opo1a3o6DAAAAADlxOHDh1WjRo0St1NISQoKCpJ0LlnBwcGXbV673a6jR48qLCys1GoXrkfuPYfcewZ59xxy7xnk3XPIvWeQd9fJyspSzZo1HTVCSSikJMfpfMHBwZe9kDpz5oyCg4N5w19m5N5zyL1nkHfPIfeeQd49h9x7Bnl3vQtd8kOWAQAAAMAiCikAAAAAsIhCCgAAAAAsopACAAAAAIsopAAAAADAIgopAAAAALCIQgoAAAAALKKQAgAAAACL+ELecijvtffcOr7fsHvcOj4AAABwtWNFCgAAAAAsopACAAAAAIvKTSEVHx8vm82m2NhYR5sxRnFxcYqKilKFChXUtm1b7dmzx2m/3NxcjRw5UlWrVlVgYKB69eqlX3755TJHDwAAAODPpFwUUlu2bNGrr76qRo0aObVPnz5dM2bM0KxZs7RlyxZFRkaqU6dOOnnypKNPbGysli9frqVLl2rjxo06deqUevTooYKCgst9GAAAAAD+JDxeSJ06dUoDBgzQf/7zH1WpUsXRbozRzJkzNWHCBPXu3VsNGjTQwoULlZOTo8WLF0uSMjMzNX/+fL3wwgvq2LGjmjZtqrfeeku7du3S2rVrPXVIAAAAAK5yHr9r34gRI9S9e3d17NhR//73vx3t+/fvV1pamjp37uxo8/f3V5s2bZScnKzhw4dr27Ztys/Pd+oTFRWlBg0aKDk5WV26dCl2ztzcXOXm5jqeZ2VlSZLsdrvsdrurD7FEdrtdxpgic7o7gst5jOVVSbmH+5F7zyDvnkPuPYO8ew659wzy7jplzaFHC6mlS5dq+/bt2rJlS5FtaWlpkqSIiAin9oiICB08eNDRx8/Pz2klq7BP4f7FiY+P16RJk4q0Hz16VGfOnLF8HBfLbrcrMzNTxhh5ef1vcTDfz+bWeX3T0906/pWgpNzD/ci9Z5B3zyH3nkHePYfcewZ5d50/XkZUGo8VUocPH9aoUaO0Zs0aBQQElNjPZnMuKowxRdrOd6E+Tz31lEaPHu14npWVpZo1ayosLEzBwcFlPIJLZ7fbZbPZFBYW5vSGz8szbp3XLzzcreNfCUrKPdyP3HsGefcccu8Z5N1zyL1nkHfXKa02+SOPFVLbtm1Tenq6mjdv7mgrKCjQ559/rlmzZmnfvn2Szq06VatWzdEnPT3dsUoVGRmpvLw8ZWRkOK1KpaenKyYmpsS5/f395e/vX6Tdy8vrsr/xbDZbkXndHQEfrnOKyz0uD3LvGeTdc8i9Z5B3zyH3nkHeXaOs+fNYljt06KBdu3YpJSXF8WjRooUGDBiglJQUXXfddYqMjFRSUpJjn7y8PG3YsMFRJDVv3ly+vr5OfVJTU7V79+5SCykAAAAAuBQeW5EKCgpSgwYNnNoCAwMVGhrqaI+NjdWUKVNUt25d1a1bV1OmTFHFihXVv39/SVKlSpU0dOhQjRkzRqGhoQoJCdHYsWPVsGFDdezY8bIfEwAAAIA/B4/fta80Tz75pE6fPq1HHnlEGRkZuu2227RmzRoFBQU5+rz44ovy8fFRnz59dPr0aXXo0EGJiYny9vb2YOQAAAAArmblqpBav36903Obzaa4uDjFxcWVuE9AQIASEhKUkJDg3uAAAAAA4P/jSjQAAAAAsIhCCgAAAAAsopACAAAAAIsopAAAAADAIgopAAAAALCIQgoAAAAALKKQAgAAAACLKKQAAAAAwCIKKQAAAACwiEIKAAAAACyikAIAAAAAiyikAAAAAMAiCikAAAAAsIhCCgAAAAAsopACAAAAAIsopAAAAADAIgopAAAAALCIQgoAAAAALKKQAgAAAACLKKQAAAAAwCIKKQAAAACwiEIKAAAAACyikAIAAAAAiyikAAAAAMAiCikAAAAAsMglhdSJEydcMQwAAAAAXBEsF1LTpk3T22+/7Xjep08fhYaGqnr16tq5c6dLgwMAAACA8shyIfXKK6+oZs2akqSkpCQlJSVp1apV6tq1q5544gmXBwgAAAAA5Y2P1R1SU1MdhdSHH36oPn36qHPnzqpVq5Zuu+02lwcIAAAAAOWN5RWpKlWq6PDhw5Kk1atXq2PHjpIkY4wKCgpcGx0AAAAAlEOWV6R69+6t/v37q27dujp27Ji6du0qSUpJSdH111/v8gABAAAAoLyxXEi9+OKLqlWrlg4fPqzp06frmmuukXTulL9HHnnE5QECAAAAQHljuZDatGmTYmNj5ePjvOujjz6q5ORklwUGAAAAAOWV5Wuk2rVrp+PHjxdpz8zMVLt27VwSFAAAAACUZ5YLKWOMbDZbkfZjx44pMDDQJUEBAAAAQHlW5lP7evfuLUmy2WwaPHiw/P39HdsKCgr0zTffKCYmxvURAgAAAEA5U+ZCqlKlSpLOrUgFBQWpQoUKjm1+fn66/fbb9be//c31EQIAAABAOVPmQmrBggWSpFq1amns2LGcxgcAAADgT8vyXfsmTpzojjgAAAAA4Iph+WYTv/32mwYOHKioqCj5+PjI29vb6QEAAAAAVzvLK1KDBw/WoUOH9PTTT6tatWrF3sEPAAAAAK5mlgupjRs36osvvlCTJk3cEA4AAAAAlH+WT+2rWbOmjDEumXzu3Llq1KiRgoODFRwcrJYtW2rVqlWO7cYYxcXFKSoqShUqVFDbtm21Z88epzFyc3M1cuRIVa1aVYGBgerVq5d++eUXl8QHAAAAAMWxXEjNnDlT48eP14EDBy558ho1amjq1KnaunWrtm7dqvbt2+uuu+5yFEvTp0/XjBkzNGvWLG3ZskWRkZHq1KmTTp486RgjNjZWy5cv19KlS7Vx40adOnVKPXr0UEFBwSXHBwAAAADFsXxqX9++fZWTk6M6deqoYsWK8vX1ddp+/PjxMo/Vs2dPp+fPPvus5s6dq6+++ko333yzZs6cqQkTJji+DHjhwoWKiIjQ4sWLNXz4cGVmZmr+/Pl688031bFjR0nSW2+9pZo1a2rt2rXq0qWL1cMDAAAAgAuyXEjNnDnTDWFIBQUFeuedd5Sdna2WLVtq//79SktLU+fOnR19/P391aZNGyUnJ2v48OHatm2b8vPznfpERUWpQYMGSk5OLrGQys3NVW5uruN5VlaWJMlut8tut7vl+Ipjt9tljCkyp7sjuJzHWF6VlHu4H7n3DPLuOeTeM8i755B7zyDvrlPWHFoupAYNGmQ5mNLs2rVLLVu21JkzZ3TNNddo+fLluvnmm5WcnCxJioiIcOofERGhgwcPSpLS0tLk5+enKlWqFOmTlpZW4pzx8fGaNGlSkfajR4/qzJkzl3pIZWa325WZmSljjLy8/neWZb6fe++E6Jue7tbxrwQl5R7uR+49g7x7Drn3DPLuOeTeM8i76/zxMqLSWC6kJOmnn37SggUL9NNPP+mll15SeHi4Vq9erZo1a6p+/fqWxqpXr55SUlJ04sQJvffeexo0aJA2bNjg2H7+7dWNMRe85fqF+jz11FMaPXq043lWVpZq1qypsLAwBQcHW4r/UtjtdtlsNoWFhTm94fPyXHMzj5L4hYe7dfwrQUm5h/uRe88g755D7j2DvHsOufcM8u46AQEBZepnuZDasGGDunbtqlatWunzzz/Xs88+q/DwcH3zzTd67bXX9O6771oaz8/PT9dff70kqUWLFtqyZYteeukljRs3TtK5Vadq1ao5+qenpztWqSIjI5WXl6eMjAynVan09HTFxMSUOKe/v7/8/f2LtHt5eV32N57NZisyr7sj4MN1TnG5x+VB7j2DvHsOufcM8u455N4zyLtrlDV/lrM8fvx4/fvf/1ZSUpL8/Pwc7e3atdOmTZusDleEMUa5ubmqXbu2IiMjlZSU5NiWl5enDRs2OIqk5s2by9fX16lPamqqdu/eXWohBQAAAACXwvKK1K5du7R48eIi7WFhYTp27Jilsf7xj3+oa9euqlmzpk6ePKmlS5dq/fr1Wr16tWw2m2JjYzVlyhTVrVtXdevW1ZQpU1SxYkX1799fklSpUiUNHTpUY8aMUWhoqEJCQjR27Fg1bNjQcRc/AAAAAHA1y4VU5cqVlZqaqtq1azu179ixQ9WrV7c01m+//aaBAwcqNTVVlSpVUqNGjbR69Wp16tRJkvTkk0/q9OnTeuSRR5SRkaHbbrtNa9asUVBQkGOMF198UT4+PurTp49Onz6tDh06KDExUd7e3lYPDQAAAADKxHIh1b9/f40bN07vvPOObDab7Ha7vvzyS40dO1YPPPCApbHmz59f6nabzaa4uDjFxcWV2CcgIEAJCQlKSEiwNDcAAAAAXCzL10g9++yzuvbaa1W9enWdOnVKN998s1q3bq2YmBj985//dEeMAAAAAFCuWF6R8vX11aJFizR58mTt2LFDdrtdTZs2Vd26dd0RHwAAAACUOxf1PVKSVKdOHdWpU8eVsQAAAADAFaFMhdTo0aP1r3/9S4GBgU5fZFucGTNmuCQwAAAAACivylRI7dixQ/n5+Y5/AwAAAMCfWZkKqXXr1hX7bwAAAAD4M7J8174hQ4bo5MmTRdqzs7M1ZMgQlwQFAAAAAOWZ5UJq4cKFOn36dJH206dP64033nBJUAAAAABQnpX5rn1ZWVkyxsgYo5MnTyogIMCxraCgQB9//LHCw8PdEiQAAAAAlCdlLqQqV64sm80mm82mG264och2m82mSZMmuTQ4AAAAACiPylxIrVu3TsYYtW/fXu+9955CQkIc2/z8/BQdHa2oqCi3BAkAAAAA5UmZC6k2bdpIkvbv36+aNWvKy8vy5VUAAAAAcFUocyFVKDo6WpKUk5OjQ4cOKS8vz2l7o0aNXBMZAAAAAJRTlgupo0eP6sEHH9SqVauK3V5QUHDJQQEAAABAeWb5/LzY2FhlZGToq6++UoUKFbR69WotXLhQdevW1cqVK90RIwAAAACUK5ZXpD777DO9//77uuWWW+Tl5aXo6Gh16tRJwcHBio+PV/fu3d0RJwAAAACUG5ZXpLKzsx3fFxUSEqKjR49Kkho2bKjt27e7NjoAAAAAKIcsF1L16tXTvn37JElNmjTRK6+8ol9//VXz5s1TtWrVXB4gAAAAAJQ3lk/ti42N1ZEjRyRJEydOVJcuXbRo0SL5+fkpMTHR1fEBAAAAQLljuZAaMGCA499NmzbVgQMH9N133+naa69V1apVXRocAAAAAJRHZT61LycnRyNGjFD16tUVHh6u/v376/fff1fFihXVrFkziigAAAAAfxplLqQmTpyoxMREde/eXf369VNSUpL+/ve/uzM2AAAAACiXynxq37JlyzR//nz169dPknT//ferVatWKigokLe3t9sCBAAAAIDypswrUocPH9add97peH7rrbfKx8fHceMJAAAAAPizKHMhVVBQID8/P6c2Hx8fnT171uVBAQAAAEB5VuZT+4wxGjx4sPz9/R1tZ86c0cMPP6zAwEBH27Jly1wbIQAAAACUM2UupAYNGlSk7f7773dpMAAAAABwJShzIbVgwQJ3xgEAAAAAV4wyXyMFAAAAADiHQgoAAAAALKKQAgAAAACLKKQAAAAAwKIyFVLNmjVTRkaGJGny5MnKyclxa1AAAAAAUJ6VqZDau3evsrOzJUmTJk3SqVOn3BoUAAAAAJRnZbr9eZMmTfTggw/qjjvukDFGzz//vK655ppi+z7zzDMuDRAAAAAAypsyFVKJiYmaOHGiPvzwQ9lsNq1atUo+PkV3tdlsFFIAAAAArnplKqTq1aunpUuXSpK8vLz06aefKjw83K2BAQAAAEB5VaZC6o/sdrs74gAAAACAK4blQkqSfvrpJ82cOVN79+6VzWbTTTfdpFGjRqlOnTqujg8AAAAAyh3L3yP1ySef6Oabb9bmzZvVqFEjNWjQQF9//bXq16+vpKQkd8QIAAAAAOWK5RWp8ePH6/HHH9fUqVOLtI8bN06dOnVyWXAAAAAAUB5ZXpHau3evhg4dWqR9yJAh+vbbb10SFAAAAACUZ5YLqbCwMKWkpBRpT0lJ4U5+AAAAAP4ULBdSf/vb3/TQQw9p2rRp+uKLL7Rx40ZNnTpVw4cP10MPPWRprPj4eN1yyy0KCgpSeHi47r77bu3bt8+pjzFGcXFxioqKUoUKFdS2bVvt2bPHqU9ubq5GjhypqlWrKjAwUL169dIvv/xi9dAAAAAAoEwsF1JPP/20nnnmGSUkJKhNmzZq3bq1Zs2apbi4OE2YMMHSWBs2bNCIESP01VdfKSkpSWfPnlXnzp2VnZ3t6DN9+nTNmDFDs2bN0pYtWxQZGalOnTrp5MmTjj6xsbFavny5li5dqo0bN+rUqVPq0aOHCgoKrB4eAAAAAFyQzRhjLnbnwmImKCjIJcEcPXpU4eHh2rBhg1q3bi1jjKKiohQbG6tx48ZJOrf6FBERoWnTpmn48OHKzMxUWFiY3nzzTfXt21eSdOTIEdWsWVMff/yxunTpUmSe3Nxc5ebmOp5nZWWpZs2aysjIUHBwsEuOpSzsdruOHj2qsLAweXn9r6bNe325W+f1G/IXt45/JSgp93A/cu8Z5N1zyL1nkHfPIfeeQd5dJysrS1WqVFFmZmaptcFFfY9UIVcVUIUyMzMlSSEhIZKk/fv3Ky0tTZ07d3b08ff3V5s2bZScnKzhw4dr27Ztys/Pd+oTFRWlBg0aKDk5udhCKj4+XpMmTSrSfvToUZ05c8alx1Qau92uzMxMGWOc3vD5fja3zuubnu7W8a8EJeUe7kfuPYO8ew659wzy7jnk3jPIu+v88cy30lxSIeVKxhiNHj1ad9xxhxo0aCBJSktLkyRFREQ49Y2IiNDBgwcdffz8/FSlSpUifQr3P99TTz2l0aNHO54XrkiFhYVd9hUpm81WdEUq76IXCcvEj5uClJh7uB+59wzy7jnk3jPIu+eQe88g764TEBBQpn7lppB69NFH9c0332jjxo1Fttlszis0xpgibecrrY+/v7/8/f2LtHt5eV32N57NZisyr7sj4MN1TnG5x+VB7j2DvHsOufcM8u455N4zyLtrlDV/5SLLI0eO1MqVK7Vu3TrVqFHD0R4ZGSlJRVaW0tPTHatUkZGRysvLU0ZGRol9AAAAAMCVLBVS+fn5ateunb7//nuXTG6M0aOPPqply5bps88+U+3atZ22165dW5GRkUpKSnK05eXlacOGDYqJiZEkNW/eXL6+vk59UlNTtXv3bkcfAAAAAHAlS6f2+fr6avfu3Rc8ra6sRowYocWLF+v9999XUFCQY+WpUqVKqlChgmw2m2JjYzVlyhTVrVtXdevW1ZQpU1SxYkX179/f0Xfo0KEaM2aMQkNDFRISorFjx6phw4bq2LGjS+IEAAAAgD+yfI3UAw88oPnz52vq1KmXPPncuXMlSW3btnVqX7BggQYPHixJevLJJ3X69Gk98sgjysjI0G233aY1a9Y43THwxRdflI+Pj/r06aPTp0+rQ4cOSkxMlLe39yXHCAAAAADns1xI5eXl6bXXXlNSUpJatGihwMBAp+0zZswo81hl+Qorm82muLg4xcXFldgnICBACQkJSkhIKPPcAAAAAHCxLBdSu3fvVrNmzSSpyLVSrjrlDwAAAADKM8uF1Lp169wRBwAAAABcMS769uc//vijPvnkE50+fVpS2U7TAwAAAICrgeVC6tixY+rQoYNuuOEGdevWTampqZKkYcOGacyYMS4PEAAAAADKG8uF1OOPPy5fX18dOnRIFStWdLT37dtXq1evdmlwAAAAAFAeWb5Gas2aNfrkk09Uo0YNp/a6devq4MGDLgsMAAAAAMoryytS2dnZTitRhX7//Xf5+/u7JCgAAAAAKM8sF1KtW7fWG2+84Xhus9lkt9v13HPPqV27di4NDgAAAADKI8un9j333HNq27attm7dqry8PD355JPas2ePjh8/ri+//NIdMQIAAABAuWJ5Rermm2/WN998o1tvvVWdOnVSdna2evfurR07dqhOnTruiBEAAAAAyhXLK1KSFBkZqUmTJrk6FgAAAAC4IlxUIZWRkaH58+dr7969stlsuummm/Tggw8qJCTE1fEBAAAAQLlj+dS+DRs2qHbt2nr55ZeVkZGh48eP6+WXX1bt2rW1YcMGd8QIAAAAAOWK5RWpESNGqE+fPpo7d668vb0lSQUFBXrkkUc0YsQI7d692+VBAgAAAEB5YnlF6qefftKYMWMcRZQkeXt7a/To0frpp59cGhwAAAAAlEeWC6lmzZpp7969Rdr37t2rJk2auCImAAAAACjXynRq3zfffOP492OPPaZRo0bpxx9/1O233y5J+uqrrzR79mxNnTrVPVECAAAAQDlSpkKqSZMmstlsMsY42p588ski/fr376++ffu6LjoAAAAAKIfKVEjt37/f3XEAAAAAwBWjTIVUdHS0u+MAAAAAgCvGRX0h76+//qovv/xS6enpstvtTtsee+wxlwQGAAAAAOWV5UJqwYIFevjhh+Xn56fQ0FDZbDbHNpvNRiEFAAAA4KpnuZB65pln9Mwzz+ipp56Sl5flu6cDAAAAwBXPciWUk5Ojfv36UUQBAAAA+NOyXA0NHTpU77zzjjtiAQAAAIArguVT++Lj49WjRw+tXr1aDRs2lK+vr9P2GTNmuCw4AAAAACiPLBdSU6ZM0SeffKJ69epJUpGbTQAAAADA1c5yITVjxgy9/vrrGjx4sBvCAQAAAIDyz/I1Uv7+/mrVqpU7YgEAAACAK4LlQmrUqFFKSEhwRywAAAAAcEWwfGrf5s2b9dlnn+nDDz9U/fr1i9xsYtmyZS4LDgAAAADKI8uFVOXKldW7d293xAIAAAAAVwTLhdSCBQvcEQcAAAAAXDEsXyMFAAAAAH92llekateuXer3Rf3888+XFBAAAAAAlHeWC6nY2Fin5/n5+dqxY4dWr16tJ554wlVxAQAAAEC5ZbmQGjVqVLHts2fP1tatWy85IAAAAAAo71x2jVTXrl313nvvuWo4AAAAACi3XFZIvfvuuwoJCXHVcAAAAABQblk+ta9p06ZON5swxigtLU1Hjx7VnDlzXBocAAAAAJRHlgupu+++2+m5l5eXwsLC1LZtW914442uigsAAAAAyi3LhdTEiRPdEQcAAAAAXDE8+oW8n3/+uXr27KmoqCjZbDatWLHCabsxRnFxcYqKilKFChXUtm1b7dmzx6lPbm6uRo4cqapVqyowMFC9evXSL7/8chmPAgAAAMCfTZkLKS8vL3l7e5f68PGxtsCVnZ2txo0ba9asWcVunz59umbMmKFZs2Zpy5YtioyMVKdOnXTy5ElHn9jYWC1fvlxLly7Vxo0bderUKfXo0UMFBQWWYgEAAACAsipz5bN8+fIStyUnJyshIUHGGEuTd+3aVV27di12mzFGM2fO1IQJE9S7d29J0sKFCxUREaHFixdr+PDhyszM1Pz58/Xmm2+qY8eOkqS33npLNWvW1Nq1a9WlSxdL8QAAAABAWZS5kLrrrruKtH333Xd66qmn9MEHH2jAgAH617/+5bLA9u/fr7S0NHXu3NnR5u/vrzZt2ig5OVnDhw/Xtm3blJ+f79QnKipKDRo0UHJycomFVG5urnJzcx3Ps7KyJEl2u112u91lx3Ahdrtdxpgic7o7gst5jOVVSbmH+5F7zyDvnkPuPYO8ew659wzy7jplzaHlm01I0pEjRzRx4kQtXLhQXbp0UUpKiho0aHAxQ5UoLS1NkhQREeHUHhERoYMHDzr6+Pn5qUqVKkX6FO5fnPj4eE2aNKlI+9GjR3XmzJlLDb3M7Ha7MjMzZYyRl9f/zrLM97OVstel801Pd+v4V4KScg/3I/eeQd49h9x7Bnn3HHLvGeTddf54GVFpLBVSmZmZmjJlihISEtSkSRN9+umnuvPOOy8qwLL643dWSedO+Tu/7XwX6vPUU09p9OjRjudZWVmqWbOmwsLCFBwcfGkBW2C322Wz2RQWFub0hs/Ls3aKpFV+4eFuHf9KUFLu4X7k3jPIu+eQe88g755D7j2DvLtOQEBAmfqVuZCaPn26pk2bpsjISC1ZsqTYU/1cKTIyUtK5Vadq1ao52tPT0x2rVJGRkcrLy1NGRobTqlR6erpiYmJKHNvf31/+/v5F2r28vC77G89msxWZ190R8OE6p7jc4/Ig955B3j2H3HsGefcccu8Z5N01ypq/MhdS48ePV4UKFXT99ddr4cKFWrhwYbH9li1bVtYhS1W7dm1FRkYqKSlJTZs2lSTl5eVpw4YNmjZtmiSpefPm8vX1VVJSkvr06SNJSk1N1e7duzV9+nSXxAEAAAAA5ytzIfXAAw9c8JQ6q06dOqUff/zR8Xz//v1KSUlRSEiIrr32WsXGxmrKlCmqW7eu6tatqylTpqhixYrq37+/JKlSpUoaOnSoxowZo9DQUIWEhGjs2LFq2LCh4y5+AAAAAOBqZS6kEhMTXT751q1b1a5dO8fzwuuWBg0apMTERD355JM6ffq0HnnkEWVkZOi2227TmjVrFBQU5NjnxRdflI+Pj/r06aPTp0+rQ4cOSkxMlLe3t8vjBQAAAADpIu/a5ypt27Yt9bunbDab4uLiFBcXV2KfgIAAJSQkKCEhwQ0RAgAAAEBRXIkGAAAAABZRSAEAAACARRRSAAAAAGARhRQAAAAAWOTRm03AM/Jee89tY/sNu8dtYwMAAADlBStSAAAAAGARhRQAAAAAWEQhBQAAAAAWUUgBAAAAgEUUUgAAAABgEYUUAAAAAFhEIQUAAAAAFlFIAQAAAIBFFFIAAAAAYBGFFAAAAABYRCEFAAAAABZRSAEAAACARRRSAAAAAGARhRQAAAAAWEQhBQAAAAAWUUgBAAAAgEUUUgAAAABgEYUUAAAAAFhEIQUAAAAAFlFIAQAAAIBFFFIAAAAAYJGPpwPA1SXvtffcOr7fsHvcOj4AAABQFqxIAQAAAIBFFFIAAAAAYBGFFAAAAABYRCEFAAAAABZRSAEAAACARRRSAAAAAGARhRQAAAAAWEQhBQAAAAAWUUgBAAAAgEUUUgAAAABgkY+nAwCsyHvtPZeMY5eU72dTXp5x/DXBb9g9LhkbAAAAVz9WpAAAAADAIgopAAAAALCIQgoAAAAALOIaKeD/c9X1VyXhGiwAAICrx1VTSM2ZM0fPPfecUlNTVb9+fc2cOVN33nmnp8MCHNxZqFGkAQAAXF5XRSH19ttvKzY2VnPmzFGrVq30yiuvqGvXrvr222917bXXejo8wO1YTQMAALi8ropCasaMGRo6dKiGDRsmSZo5c6Y++eQTzZ07V/Hx8R6ODkBpWKnzDPLuGeQdAK4eV3whlZeXp23btmn8+PFO7Z07d1ZycnKx++Tm5io3N9fxPDMzU5J04sQJ2e129wV7HrvdrqysLPn5+cnL63/3/cg7nXPZYvizskvKKrDJ9w/fI4VSJLzpsqHsko752aTLkXsXxn2lI++ec9lyfwXn3W9gT5eP+cefsWcXfeTy8Qu5I/YrXWHu9U6S297zV3Le8978wC3jFv5f4/fXbk6/V7qSu2IvVF5e16ysLEmSMabUfld8IfX777+roKBAERERTu0RERFKS0srdp/4+HhNmjSpSHt0dLRbYgQAAKV4zNMBXIIrOfYrGXkv2cOeDuASlLPX9eTJk6pUqVKJ26/4QqqQzWZzem6MKdJW6KmnntLo0aMdz+12u44fP67Q0NAS93GHrKws1axZU4cPH1ZwcPBlmxfk3pPIvWeQd88h955B3j2H3HsGeXcdY4xOnjypqKioUvtd8YVU1apV5e3tXWT1KT09vcgqVSF/f3/5+/s7tVWuXNldIV5QcHAwb3gPIfeeQ+49g7x7Drn3DPLuOeTeM8i7a5S2ElXoir88xM/PT82bN1dSUpJTe1JSkmJiYjwUFQAAAICr2RW/IiVJo0eP1sCBA9WiRQu1bNlSr776qg4dOqSHH76STxIFAAAAUF5dFYVU3759dezYMU2ePFmpqalq0KCBPv7443J/8wh/f39NnDixyGmGcD9y7znk3jPIu+eQe88g755D7j2DvF9+NnOh+/oBAAAAAJxc8ddIAQAAAMDlRiEFAAAAABZRSAEAAACARRRSAAAAAGARhZQHzZkzR7Vr11ZAQICaN2+uL774wtMhXVXi4+N1yy23KCgoSOHh4br77ru1b98+pz7GGMXFxSkqKkoVKlRQ27ZttWfPHg9FfHWKj4+XzWZTbGyso428u8+vv/6q+++/X6GhoapYsaKaNGmibdu2ObaTe/c4e/as/vnPf6p27dqqUKGCrrvuOk2ePFl2u93Rh9y7xueff66ePXsqKipKNptNK1ascNpeljzn5uZq5MiRqlq1qgIDA9WrVy/98ssvl/Eorjyl5T0/P1/jxo1Tw4YNFRgYqKioKD3wwAM6cuSI0xjk/eJc6D3/R8OHD5fNZtPMmTOd2sm9e1BIecjbb7+t2NhYTZgwQTt27NCdd96prl276tChQ54O7aqxYcMGjRgxQl999ZWSkpJ09uxZde7cWdnZ2Y4+06dP14wZMzRr1ixt2bJFkZGR6tSpk06ePOnByK8eW7Zs0auvvqpGjRo5tZN398jIyFCrVq3k6+urVatW6dtvv9ULL7ygypUrO/qQe/eYNm2a5s2bp1mzZmnv3r2aPn26nnvuOSUkJDj6kHvXyM7OVuPGjTVr1qxit5clz7GxsVq+fLmWLl2qjRs36tSpU+rRo4cKCgou12FccUrLe05OjrZv366nn35a27dv17Jly/T999+rV69eTv3I+8W50Hu+0IoVK/T1118rKiqqyDZy7yYGHnHrrbeahx9+2KntxhtvNOPHj/dQRFe/9PR0I8ls2LDBGGOM3W43kZGRZurUqY4+Z86cMZUqVTLz5s3zVJhXjZMnT5q6deuapKQk06ZNGzNq1ChjDHl3p3Hjxpk77rijxO3k3n26d+9uhgwZ4tTWu3dvc//99xtjyL27SDLLly93PC9Lnk+cOGF8fX3N0qVLHX1+/fVX4+XlZVavXn3ZYr+SnZ/34mzevNlIMgcPHjTGkHdXKSn3v/zyi6levbrZvXu3iY6ONi+++KJjG7l3H1akPCAvL0/btm1T586dndo7d+6s5ORkD0V19cvMzJQkhYSESJL279+vtLQ0p9fB399fbdq04XVwgREjRqh79+7q2LGjUzt5d5+VK1eqRYsW+utf/6rw8HA1bdpU//nPfxzbyb373HHHHfr000/1/fffS5J27typjRs3qlu3bpLI/eVSljxv27ZN+fn5Tn2ioqLUoEEDXgsXyszMlM1mc6yIk3f3sdvtGjhwoJ544gnVr1+/yHZy7z4+ng7gz+j3339XQUGBIiIinNojIiKUlpbmoaiubsYYjR49WnfccYcaNGggSY5cF/c6HDx48LLHeDVZunSptm/fri1bthTZRt7d5+eff9bcuXM1evRo/eMf/9DmzZv12GOPyd/fXw888AC5d6Nx48YpMzNTN954o7y9vVVQUKBnn31W9913nyTe95dLWfKclpYmPz8/ValSpUgffga7xpkzZzR+/Hj1799fwcHBksi7O02bNk0+Pj567LHHit1O7t2HQsqDbDab03NjTJE2uMajjz6qb775Rhs3biyyjdfBtQ4fPqxRo0ZpzZo1CggIKLEfeXc9u92uFi1aaMqUKZKkpk2bas+ePZo7d64eeOABRz9y73pvv/223nrrLS1evFj169dXSkqKYmNjFRUVpUGDBjn6kfvL42LyzGvhGvn5+erXr5/sdrvmzJlzwf7k/dJs27ZNL730krZv3245j+T+0nFqnwdUrVpV3t7eRf4KkJ6eXuSvaLh0I0eO1MqVK7Vu3TrVqFHD0R4ZGSlJvA4utm3bNqWnp6t58+by8fGRj4+PNmzYoJdfflk+Pj6O3JJ316tWrZpuvvlmp7abbrrJcRMb3vPu88QTT2j8+PHq16+fGjZsqIEDB+rxxx9XfHy8JHJ/uZQlz5GRkcrLy1NGRkaJfXBx8vPz1adPH+3fv19JSUmO1SiJvLvLF198ofT0dF177bWOn7kHDx7UmDFjVKtWLUnk3p0opDzAz89PzZs3V1JSklN7UlKSYmJiPBTV1ccYo0cffVTLli3TZ599ptq1azttr127tiIjI51eh7y8PG3YsIHX4RJ06NBBu3btUkpKiuPRokULDRgwQCkpKbruuuvIu5u0atWqyC3+v//+e0VHR0viPe9OOTk58vJy/pHq7e3tuP05ub88ypLn5s2by9fX16lPamqqdu/ezWtxCQqLqB9++EFr165VaGio03by7h4DBw7UN9984/QzNyoqSk888YQ++eQTSeTerTx0k4s/vaVLlxpfX18zf/588+2335rY2FgTGBhoDhw44OnQrhp///vfTaVKlcz69etNamqq45GTk+PoM3XqVFOpUiWzbNkys2vXLnPfffeZatWqmaysLA9GfvX54137jCHv7rJ582bj4+Njnn32WfPDDz+YRYsWmYoVK5q33nrL0Yfcu8egQYNM9erVzYcffmj2799vli1bZqpWrWqefPJJRx9y7xonT540O3bsMDt27DCSzIwZM8yOHTscd4crS54ffvhhU6NGDbN27Vqzfft20759e9O4cWNz9uxZTx1WuVda3vPz802vXr1MjRo1TEpKitPP3NzcXMcY5P3iXOg9f77z79pnDLl3FwopD5o9e7aJjo42fn5+plmzZo7bcsM1JBX7WLBggaOP3W43EydONJGRkcbf39+0bt3a7Nq1y3NBX6XOL6TIu/t88MEHpkGDBsbf39/ceOON5tVXX3XaTu7dIysry4waNcpce+21JiAgwFx33XVmwoQJTr9EknvXWLduXbH/tw8aNMgYU7Y8nz592jz66KMmJCTEVKhQwfTo0cMcOnTIA0dz5Sgt7/v37y/xZ+66descY5D3i3Oh9/z5iiukyL172Iwx5nKsfAEAAADA1YJrpAAAAADAIgopAAAAALCIQgoAAAAALKKQAgAAAACLKKQAAAAAwCIKKQAAAACwiEIKAAAAACyikAIAAAAAiyikAAAeceDAAdlsNqWkpHg6FIfvvvtOt99+uwICAtSkSZPLOndiYqIqV65c5v7r16+XzWbTiRMnSu1Xq1YtzZw585JiAwAURSEFAH9SgwcPls1m09SpU53aV6xYIZvN5qGoPGvixIkKDAzUvn379Omnn5baNzk5Wd7e3vq///s/y/MUV9z07dtX33//fZnHiImJUWpqqipVqiSp5EJsy5YteuihhyzHCAAoHYUUAPyJBQQEaNq0acrIyPB0KC6Tl5d30fv+9NNPuuOOOxQdHa3Q0NBS+77++usaOXKkNm7cqEOHDl30nIUqVKig8PDwMvf38/NTZGTkBYvesLAwVaxY8VLDAwCch0IKAP7EOnbsqMjISMXHx5fYJy4urshpbjNnzlStWrUczwcPHqy7775bU6ZMUUREhCpXrqxJkybp7NmzeuKJJxQSEqIaNWro9ddfLzL+d999p5iYGAUEBKh+/fpav3690/Zvv/1W3bp10zXXXKOIiAgNHDhQv//+u2N727Zt9eijj2r06NGqWrWqOnXqVOxx2O12TZ48WTVq1JC/v7+aNGmi1atXO7bbbDZt27ZNkydPls1mU1xcXIk5yc7O1n//+1/9/e9/V48ePZSYmFikz8qVK9WiRQsFBASoatWq6t27tyPegwcP6vHHH5fNZnMUQn9cUdq3b59sNpu+++47pzFnzJihWrVqyRjjdGrf+vXr9eCDDyozM9MxZmH8569+ZWZm6qGHHlJ4eLiCg4PVvn177dy507F9586dateunYKCghQcHKzmzZtr69atJeYCAP6sKKQA4E/M29tbU6ZMUUJCgn755ZdLGuuzzz7TkSNH9Pnnn2vGjBmKi4tTjx49VKVKFX399dd6+OGH9fDDD+vw4cNO+z3xxBMaM2aMduzYoZiYGPXq1UvHjh2TJKWmpqpNmzZq0qSJtm7dqtWrV+u3335Tnz59nMZYuHChfHx89OWXX+qVV14pNr6XXnpJL7zwgp5//nl988036tKli3r16qUffvjBMVf9+vU1ZswYpaamauzYsSUe69tvv6169eqpXr16uv/++7VgwQIZYxzbP/roI/Xu3Vvdu3fXjh079Omnn6pFixaSpGXLlqlGjRqaPHmyUlNTlZqaWmT8evXqqXnz5lq0aJFT++LFi9W/f/8iq1AxMTGaOXOmgoODHWMWF78xRt27d1daWpo+/vhjbdu2Tc2aNVOHDh10/PhxSdKAAQNUo0YNbdmyRdu2bdP48ePl6+tbYi4A4E/LAAD+lAYNGmTuuusuY4wxt99+uxkyZIgxxpjly5ebP/54mDhxomncuLHTvi+++KKJjo52Gis6OtoUFBQ42urVq2fuvPNOx/OzZ8+awMBAs2TJEmOMMfv37zeSzNSpUx198vPzTY0aNcy0adOMMcY8/fTTpnPnzk5zHz582Egy+/btM8YY06ZNG9OkSZMLHm9UVJR59tlnndpuueUW88gjjzieN27c2EycOPGCY8XExJiZM2c6Yq5atapJSkpybG/ZsqUZMGBAiftHR0ebF1980altwYIFplKlSo7nM2bMMNddd53j+b59+4wks2fPHmOMMevWrTOSTEZGRrH7FzfXp59+aoKDg82ZM2ec+tSpU8e88sorxhhjgoKCTGJiYqnHDwAwhhUpAICmTZumhQsX6ttvv73oMerXry8vr//9WImIiFDDhg0dz729vRUaGqr09HSn/Vq2bOn4t4+Pj1q0aKG9e/dKkrZt26Z169bpmmuucTxuvPFGSeeuZypUuNpTkqysLB05ckStWrVyam/VqpVjrrLat2+fNm/erH79+jli7tu3r9NpiykpKerQoYOlcc/Xr18/HTx4UF999ZUkadGiRWrSpIluvvnmix5z27ZtOnXqlEJDQ51yun//fkc+R48erWHDhqljx46aOnWqU54BAP/j4+kAAACe17p1a3Xp0kX/+Mc/NHjwYKdtXl5eTqetSVJ+fn6RMc4//ctmsxXbZrfbLxhP4alrdrtdPXv21LRp04r0qVatmuPfgYGBFxzzj+MWMsZYvkPh/PnzdfbsWVWvXt1pHF9fX2VkZKhKlSqqUKGCpTGLU61aNbVr106LFy/W7bffriVLlmj48OGXNKbdble1atWKXIcmyXF9VlxcnPr376+PPvpIq1at0sSJE7V06VL95S9/uaS5AeBqw4oUAECSNHXqVH3wwQdKTk52ag8LC1NaWppTMeXK734qXHGRpLNnz2rbtm2OVadmzZppz549qlWrlq6//nqnR1mLJ0kKDg5WVFSUNm7c6NSenJysm266qczjnD17Vm+88YZeeOEFpaSkOB47d+5UdHS045qmRo0alXr7dD8/PxUUFFxwvgEDBujtt9/Wpk2b9NNPPzlWwS52zGbNmiktLU0+Pj5F8lm1alVHvxtuuEGPP/641qxZo969e2vBggUXjBUA/mwopAAAkqSGDRtqwIABSkhIcGpv27atjh49qunTp+unn37S7NmztWrVKpfNO3v2bC1fvlzfffedRowYoYyMDA0ZMkSSNGLECB0/flz33XefNm/erJ9//llr1qzRkCFDylSI/NETTzyhadOm6e2339a+ffs0fvx4paSkaNSoUWUe48MPP1RGRoaGDh2qBg0aOD3uvfdezZ8/X9K576NasmSJJk6cqL1792rXrl2aPn26Y5xatWrp888/16+//up0B8Lz9e7dW1lZWfr73/+udu3aOa2Cna9WrVo6deqUPv30U/3+++/Kyckp0qdjx45q2bKl7r77bn3yySc6cOCAkpOT9c9//lNbt27V6dOn9eijj2r9+vU6ePCgvvzyS23ZssVSsQkAfxYUUgAAh3/9619FTuO76aabNGfOHM2ePVuNGzfW5s2bS72jnVVTp07VtGnT1LhxY33xxRd6//33HasjUVFR+vLLL1VQUKAuXbqoQYMGGjVqlCpVquR0PVZZPPbYYxozZozGjBmjhg0bavXq1Vq5cqXq1q1b5jHmz5+vjh07Or4E94/uuecepaSkaPv27Wrbtq3eeecdrVy5Uk2aNFH79u319ddfO/pOnjxZBw4cUJ06dRQWFlbifMHBwerZs6d27typAQMGlBpbTEyMHn74YfXt21dhYWFOhVshm82mjz/+WK1bt9aQIUN0ww03qF+/fjpw4IAiIiLk7e2tY8eO6YEHHtANN9ygPn36qGvXrpo0aVKZcwQAfxY2c/5PTAAAAABAqViRAgAAAACLKKQAAAAAwCIKKQAAAACwiEIKAAAAACyikAIAAAAAiyikAAAAAMAiCikAAAAAsIhCCgAAAAAsopACAAAAAIsopAAAAADAIgopAAAAALDo/wEHFKwCuxjW2gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Analyze activity distribution per patient\n", "activities_per_patient = df.groupby('aio_patient_id').size()\n", "\n", "print(\"📈 ACTIVITIES PER PATIENT DISTRIBUTION\")\n", "print(\"=\" * 50)\n", "print(f\"Minimum activities: {activities_per_patient.min()}\")\n", "print(f\"Maximum activities: {activities_per_patient.max()}\")\n", "print(f\"Median activities: {activities_per_patient.median():.0f}\")\n", "print(f\"Mean activities: {activities_per_patient.mean():.1f}\")\n", "\n", "# Create a simple histogram\n", "plt.figure(figsize=(10, 3))\n", "plt.hist(activities_per_patient, bins=30, alpha=0.7)\n", "plt.title('Distribution of Activities per Patient')\n", "plt.xlabel('Number of Activities')\n", "plt.ylabel('Number of Patients')\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f4104f06", "metadata": {}, "source": ["## 4. Medical Codes Analysis\n", "Understanding the procedures and services in our data"]}, {"cell_type": "code", "execution_count": 9, "id": "fa53b782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🩺 MEDICAL CODES ANALYSIS\n", "==================================================\n", "Unique medical codes: 769\n", "5-digit numeric codes (CPT-like): 3,230 (64.6%)\n", "Other code formats: 1,769 (35.4%)\n"]}], "source": ["# Analyze medical codes\n", "print(\"🩺 MEDICAL CODES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "unique_codes = df['code_activity'].nunique()\n", "print(f\"Unique medical codes: {unique_codes:,}\")\n", "\n", "# Check if codes look like CPT codes (5-digit numbers)\n", "df['is_numeric_5digit'] = df['code_activity'].astype(str).str.match(r'^\\d{5}$')\n", "cpt_like_codes = df['is_numeric_5digit'].sum()\n", "\n", "print(f\"5-digit numeric codes (CPT-like): {cpt_like_codes:,} ({cpt_like_codes/len(df)*100:.1f}%)\")\n", "print(f\"Other code formats: {len(df) - cpt_like_codes:,} ({(len(df) - cpt_like_codes)/len(df)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 10, "id": "ddc3930d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏆 TOP 10 MOST COMMON MEDICAL CODES\n", "==================================================\n", " 1. A4649: 470 times (9.4%) - Syringe, Disposable, 20ML Box50...\n", " 2. 99213: 462 times (9.2%) - Office or other outpatient visit for the evaluatio...\n", " 3. 97110: 231 times (4.6%) - Therapeutic procedure, 1 or more areas,each 15 min...\n", " 4. 97140: 225 times (4.5%) - Manual therapy techniques (eg, mobilization/ manip...\n", " 5. 97014: 220 times (4.4%) - Application of a modality to 1 or more areas; elec...\n", " 6. 99214: 195 times (3.9%) - Office or other outpatient visit for the evaluatio...\n", " 7. 99203: 147 times (2.9%) - Office or other outpatient visit for the evaluatio...\n", " 8. 99212: 114 times (2.3%) - Office or other outpatient visit for the evaluatio...\n", " 9. 97010: 88 times (1.8%) - Application of a modality to 1 or more areas; hot ...\n", "10. 99204: 86 times (1.7%) - Office or other outpatient visit for the evaluatio...\n"]}], "source": ["# Show most common codes\n", "print(\"🏆 TOP 10 MOST COMMON MEDICAL CODES\")\n", "print(\"=\" * 50)\n", "\n", "top_codes = df['code_activity'].value_counts().head(10)\n", "for i, (code, count) in enumerate(top_codes.items(), 1):\n", "    pct = (count / len(df)) * 100\n", "    # Get a sample description\n", "    desc = df[df['code_activity'] == code]['activity_desc'].iloc[0]\n", "    print(f\"{i:2d}. {code}: {count:,} times ({pct:.1f}%) - {desc[:50]}...\")"]}, {"cell_type": "code", "execution_count": 11, "id": "6ecaf3a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 CODE PATTERN ANALYSIS\n", "==================================================\n", "Sample codes to understand patterns:\n", "  • 87880\n", "  • 99203\n", "  • 97014\n", "  • 97110\n", "  • 97140\n", "  • 97161\n", "  • 99204\n", "  • 17110\n", "  • 99213\n", "  • 99212\n", "  • 69210\n", "  • 97010\n", "  • 99214\n", "  • 72100\n", "  • 71046\n", "  • 94010\n", "  • 94640\n", "  • B46-4387-00778-01\n", "  • C81-0459-03093-01\n", "  • 73610\n", "\n", "Code length distribution:\n", "  2 characters: 5 codes (0.1%)\n", "  3 characters: 6 codes (0.1%)\n", "  4 characters: 28 codes (0.6%)\n", "  5 characters: 3,773 codes (75.5%)\n", "  6 characters: 33 codes (0.7%)\n", "  17 characters: 1,154 codes (23.1%)\n"]}], "source": ["# Analyze code patterns\n", "print(\"🔍 CODE PATTERN ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Sample different code formats\n", "sample_codes = df['code_activity'].unique()[:20]\n", "print(\"Sample codes to understand patterns:\")\n", "for code in sample_codes:\n", "    print(f\"  • {code}\")\n", "\n", "# Check code lengths\n", "code_lengths = df['code_activity'].astype(str).str.len()\n", "print(f\"\\nCode length distribution:\")\n", "length_counts = code_lengths.value_counts().sort_index()\n", "for length, count in length_counts.head(10).items():\n", "    pct = (count / len(df)) * 100\n", "    print(f\"  {length} characters: {count:,} codes ({pct:.1f}%)\")"]}, {"cell_type": "markdown", "id": "c935767c", "metadata": {}, "source": ["## 5. Financial Information Analysis\n", "Understanding the cost and payment data"]}, {"cell_type": "code", "execution_count": 12, "id": "17c83368", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💰 FINANCIAL DATA ANALYSIS\n", "==================================================\n", "Available financial columns: ['gross', 'net', 'patient_share', 'payment_amount']\n", "\n", "GROSS:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $705,006.09\n", "  Average: $141.03\n", "  Median: $52.20\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,080\n", "\n", "NET:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $660,289.32\n", "  Average: $132.08\n", "  Median: $45.00\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,080\n", "\n", "PATIENT_SHARE:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $44,716.77\n", "  Average: $8.95\n", "  Median: $0.00\n", "  Range: $0.00 - $193.50\n", "  Zero values: 3,491\n", "\n", "PAYMENT_AMOUNT:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $545,183.74\n", "  Average: $109.06\n", "  Median: $33.35\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,479\n"]}], "source": ["# Analyze financial columns\n", "financial_cols = ['gross', 'net', 'patient_share', 'payment_amount']\n", "available_financial = [col for col in financial_cols if col in df.columns]\n", "\n", "print(\"💰 FINANCIAL DATA ANALYSIS\")\n", "print(\"=\" * 50)\n", "print(f\"Available financial columns: {available_financial}\")\n", "\n", "for col in available_financial:\n", "    data = df[col].dropna()\n", "    if len(data) > 0:\n", "        print(f\"\\n{col.upper()}:\")\n", "        print(f\"  Records with data: {len(data):,} ({len(data)/len(df)*100:.1f}%)\")\n", "        print(f\"  Total amount: ${data.sum():,.2f}\")\n", "        print(f\"  Average: ${data.mean():.2f}\")\n", "        print(f\"  Median: ${data.median():.2f}\")\n", "        print(f\"  Range: ${data.min():.2f} - ${data.max():.2f}\")\n", "        print(f\"  Zero values: {(data == 0).sum():,}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "31140647", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💵 COST RANGE ANALYSIS\n", "==============================\n", "Free services ($0): 1,080\n", "Low cost ($1-$100): 2,388\n", "Medium cost ($101-$500): 1,332\n", "High cost ($501-$1,000): 101\n", "Very high cost ($1,000+): 98\n"]}], "source": ["# Visualize financial distribution\n", "if 'gross' in df.columns:\n", "    plt.figure(figsize=(12, 5))\n", "    \n", "    # Histogram of gross amounts\n", "    plt.subplot(1, 2, 1)\n", "    gross_data = df['gross'].dropna()\n", "    plt.hist(gross_data, bins=50, edgecolor='black', alpha=0.7)\n", "    plt.title('Distribution of Gross Amounts')\n", "    plt.xlabel('Amount ($)')\n", "    plt.ylabel('Frequency')\n", "    plt.yscale('log')  # Log scale for better visualization\n", "    \n", "    # Box plot\n", "    plt.subplot(1, 2, 2)\n", "    plt.boxplot(gross_data)\n", "    plt.title('Gross Amount Box Plot')\n", "    plt.ylabel('Amount ($)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary statistics for cost ranges\n", "    print(\"\\n💵 COST RANGE ANALYSIS\")\n", "    print(\"=\" * 30)\n", "    print(f\"Free services ($0): {(gross_data == 0).sum():,}\")\n", "    print(f\"Low cost ($1-$100): {((gross_data > 0) & (gross_data <= 100)).sum():,}\")\n", "    print(f\"Medium cost ($101-$500): {((gross_data > 100) & (gross_data <= 500)).sum():,}\")\n", "    print(f\"High cost ($501-$1,000): {((gross_data > 500) & (gross_data <= 1000)).sum():,}\")\n", "    print(f\"Very high cost ($1,000+): {(gross_data > 1000).sum():,}\")"]}, {"cell_type": "markdown", "id": "4aace4e8", "metadata": {}, "source": ["## 6. Healthcare Providers Analysis\n", "Understanding who provides the services"]}, {"cell_type": "code", "execution_count": 14, "id": "7700f7c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👨‍⚕️ HEALTHCARE PROVIDERS ANALYSIS\n", "==================================================\n", "Unique clinicians: 270\n", "Records with clinician info: 4,999 (100.0%)\n", "\n", "Top 10 most active clinicians:\n", " 1. GD10670: 269 activities (5.4%)\n", " 2. GD21895: 256 activities (5.1%)\n", " 3. GD5891: 250 activities (5.0%)\n", " 4. GD20346: 179 activities (3.6%)\n", " 5. GD25091: 156 activities (3.1%)\n", " 6. GD8357: 134 activities (2.7%)\n", " 7. GD37430: 102 activities (2.0%)\n", " 8. GD25783: 95 activities (1.9%)\n", " 9. GD24090: 94 activities (1.9%)\n", "10. GD20029: 93 activities (1.9%)\n", "\n", "Unique institutions: 10\n", "\n", "Institutions:\n", "  • BDSC: 2,934 activities (58.7%)\n", "  • BURJEEL-AL AIN: 1,075 activities (21.5%)\n", "  • BURJEEL-AD: 586 activities (11.7%)\n", "  • BURJEEL ASHAREJ: 176 activities (3.5%)\n", "  • BMC-SHAMKHA: 162 activities (3.2%)\n", "  • LLH MC -MS: 28 activities (0.6%)\n", "  • LLH OASIS: 16 activities (0.3%)\n", "  • BURJEEL-SHARJAH: 11 activities (0.2%)\n", "  • LLH-MS: 8 activities (0.2%)\n", "  • BMC-BARARI: 3 activities (0.1%)\n"]}], "source": ["# Analyze healthcare providers\n", "print(\"👨‍⚕️ HEALTHCARE PROVIDERS ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "if 'clinician' in df.columns:\n", "    unique_clinicians = df['clinician'].nunique()\n", "    records_with_clinician = df['clinician'].notna().sum()\n", "    \n", "    print(f\"Unique clinicians: {unique_clinicians:,}\")\n", "    print(f\"Records with clinician info: {records_with_clinician:,} ({records_with_clinician/len(df)*100:.1f}%)\")\n", "    \n", "    # Top clinicians by volume\n", "    top_clinicians = df['clinician'].value_counts().head(10)\n", "    print(f\"\\nTop 10 most active clinicians:\")\n", "    for i, (clinician, count) in enumerate(top_clinicians.items(), 1):\n", "        pct = (count / len(df)) * 100\n", "        print(f\"{i:2d}. {clinician}: {count:,} activities ({pct:.1f}%)\")\n", "\n", "if 'institution_name' in df.columns:\n", "    unique_institutions = df['institution_name'].nunique()\n", "    print(f\"\\nUnique institutions: {unique_institutions:,}\")\n", "    \n", "    # Show all institutions if not too many\n", "    if unique_institutions <= 15:\n", "        institutions = df['institution_name'].value_counts()\n", "        print(f\"\\nInstitutions:\")\n", "        for institution, count in institutions.items():\n", "            pct = (count / len(df)) * 100\n", "            print(f\"  • {institution}: {count:,} activities ({pct:.1f}%)\")"]}, {"cell_type": "markdown", "id": "c3773c09", "metadata": {}, "source": ["## 7. Date and Time Analysis\n", "Understanding the temporal aspects of our data"]}, {"cell_type": "code", "execution_count": 15, "id": "4c80305f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 DATE ANALYSIS\n", "==================================================\n", "Date columns found: ['start_activity_date', 'resub_date', 'remittance_date', 'encounter_start_date', 'encounter_end_date', 'submission_date', 'year_encounter_end_date']\n", "\n", "start_activity_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "resub_date:\n", "  Records with dates: 1,146 (22.9%)\n", "  Sample dates: ['30/09/2023', '30/09/2023', '30/09/2023', '30/09/2023', '30/09/2023']\n", "\n", "remittance_date:\n", "  Records with dates: 4,993 (99.9%)\n", "  Sample dates: ['07/10/2023', '07/10/2023', '28/07/2023', '28/07/2023', '28/07/2023']\n", "\n", "encounter_start_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "encounter_end_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "submission_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['19/06/2023', '19/06/2023', '19/06/2023', '19/06/2023', '19/06/2023']\n", "\n", "year_encounter_end_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: [2023, 2023, 2023, 2023, 2023]\n"]}], "source": ["# Analyze date columns\n", "date_columns = [col for col in df.columns if 'date' in col.lower()]\n", "\n", "print(\"📅 DATE ANALYSIS\")\n", "print(\"=\" * 50)\n", "print(f\"Date columns found: {date_columns}\")\n", "\n", "for col in date_columns:\n", "    if col in df.columns:\n", "        print(f\"\\n{col}:\")\n", "        non_null_dates = df[col].notna().sum()\n", "        print(f\"  Records with dates: {non_null_dates:,} ({non_null_dates/len(df)*100:.1f}%)\")\n", "        \n", "        if non_null_dates > 0:\n", "            # Sample of date formats\n", "            sample_dates = df[col].dropna().head(5).tolist()\n", "            print(f\"  Sample dates: {sample_dates}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "83a9aac0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏥 ENCOUNTER PATTERNS\n", "==================================================\n", "Total encounters with dates: 4,999\n", "Same-day encounters: 4,908 (98.2%)\n", "Multi-day encounters: 91 (1.8%)\n"]}], "source": ["# If we have encounter dates, analyze patterns\n", "if 'encounter_start_date' in df.columns and 'encounter_end_date' in df.columns:\n", "    encounter_data = df[['encounter_start_date', 'encounter_end_date']].dropna()\n", "    \n", "    if len(encounter_data) > 0:\n", "        print(\"🏥 ENCOUNTER PATTERNS\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Check for same-day vs multi-day encounters\n", "        same_start_end = (encounter_data['encounter_start_date'] == encounter_data['encounter_end_date']).sum()\n", "        \n", "        print(f\"Total encounters with dates: {len(encounter_data):,}\")\n", "        print(f\"Same-day encounters: {same_start_end:,} ({same_start_end/len(encounter_data)*100:.1f}%)\")\n", "        print(f\"Multi-day encounters: {len(encounter_data)-same_start_end:,} ({(len(encounter_data)-same_start_end)/len(encounter_data)*100:.1f}%)\")"]}, {"cell_type": "markdown", "id": "834a8790", "metadata": {}, "source": ["## 8. OMOP CDM Mapping Potential\n", "Understanding how our data could fit into OMOP Common Data Model"]}, {"cell_type": "code", "execution_count": 17, "id": "7d394e0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 OMOP CDM MAPPING ASSESSMENT\n", "==================================================\n", "The OMOP Common Data Model has several key tables. Let's see how our data fits:\n", "\n", "👥 PERSON Table:\n", "   Purpose: Store patient demographics\n", "   Our data: ✅ aio_patient_id (can be person_id)\n", "   Missing: ❌ birth_date, gender, race (common in anonymized data)\n", "   Status: Basic implementation possible\n", "\n", "🏥 VISIT_OCCURRENCE Table:\n", "   Purpose: Store healthcare encounters\n", "   Our data: ✅ case (visit_occurrence_id), encounter dates\n", "   Our data: ✅ case_type (visit_type)\n", "   Status: Excellent mapping potential\n", "\n", "🩺 PROCEDURE_OCCURRENCE Table:\n", "   Purpose: Store medical procedures and services\n", "   Our data: ✅ code_activity (procedure codes)\n", "   Our data: ✅ activity_desc (procedure descriptions)\n", "   Our data: ✅ start_activity_date (procedure_date)\n", "   Code quality: 64.6% appear to be standard CPT codes\n", "   Status: Strong mapping potential\n", "\n", "💰 COST Table:\n", "   Purpose: Store financial information\n", "   Our data: ✅ gross (total_charge)\n", "   Our data: ✅ payment_amount (total_paid)\n", "   Our data: ✅ patient_share (patient_copay)\n", "   Status: Excellent financial data available\n", "\n", "👨‍⚕️ PROVIDER Table:\n", "   Purpose: Store healthcare provider information\n", "   Our data: ✅ clinician (provider_name)\n", "   Missing: ❌ provider specialty, credentials\n", "   Status: Basic provider tracking possible\n"]}], "source": ["# OMOP mapping assessment - simple and clear\n", "print(\"🎯 OMOP CDM MAPPING ASSESSMENT\")\n", "print(\"=\" * 50)\n", "\n", "print(\"The OMOP Common Data Model has several key tables. Let's see how our data fits:\")\n", "print()\n", "\n", "# PERSON table potential\n", "print(\"👥 PERSON Table:\")\n", "print(\"   Purpose: Store patient demographics\")\n", "print(\"   Our data: ✅ aio_patient_id (can be person_id)\")\n", "print(\"   Missing: ❌ birth_date, gender, race (common in anonymized data)\")\n", "print(\"   Status: Basic implementation possible\")\n", "print()\n", "\n", "# VISIT_OCCURRENCE potential  \n", "print(\"🏥 VISIT_OCCURRENCE Table:\")\n", "print(\"   Purpose: Store healthcare encounters\")\n", "print(\"   Our data: ✅ case (visit_occurrence_id), encounter dates\")\n", "if 'case_type' in df.columns:\n", "    print(\"   Our data: ✅ case_type (visit_type)\")\n", "print(\"   Status: Excellent mapping potential\")\n", "print()\n", "\n", "# PROCEDURE_OCCURRENCE potential\n", "print(\"🩺 PROCEDURE_OCCURRENCE Table:\")\n", "print(\"   Purpose: Store medical procedures and services\")\n", "print(\"   Our data: ✅ code_activity (procedure codes)\")\n", "print(\"   Our data: ✅ activity_desc (procedure descriptions)\")\n", "print(\"   Our data: ✅ start_activity_date (procedure_date)\")\n", "print(f\"   Code quality: {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes\")\n", "print(\"   Status: Strong mapping potential\")\n", "print()\n", "\n", "# COST table potential\n", "print(\"💰 COST Table:\")\n", "print(\"   Purpose: Store financial information\")\n", "if 'gross' in df.columns:\n", "    print(\"   Our data: ✅ gross (total_charge)\")\n", "if 'payment_amount' in df.columns:\n", "    print(\"   Our data: ✅ payment_amount (total_paid)\")\n", "if 'patient_share' in df.columns:\n", "    print(\"   Our data: ✅ patient_share (patient_copay)\")\n", "print(\"   Status: Excellent financial data available\")\n", "print()\n", "\n", "# PROVIDER table potential\n", "print(\"👨‍⚕️ PROVIDER Table:\")\n", "print(\"   Purpose: Store healthcare provider information\")\n", "if 'clinician' in df.columns:\n", "    print(\"   Our data: ✅ clinician (provider_name)\")\n", "print(\"   Missing: ❌ provider specialty, credentials\")\n", "print(\"   Status: Basic provider tracking possible\")"]}, {"cell_type": "code", "execution_count": 18, "id": "6bbc2b6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 OMOP IMPLEMENTATION READINESS SUMMARY\n", "==================================================\n", "Table readiness scores (0-100%):\n", "  PERSON: 60% 🟡 MEDIUM\n", "  VISIT_OCCURRENCE: 85% 🟢 HIGH\n", "  PROCEDURE_OCCURRENCE: 80% 🟢 HIGH\n", "  COST: 90% 🟢 HIGH\n", "  PROVIDER: 65% 🟡 MEDIUM\n", "  DRUG_EXPOSURE: 10% 🔴 LOW\n", "  CONDITION_OCCURRENCE: 20% 🔴 LOW\n", "\n", "Overall assessment:\n", "  Average readiness: 59%\n", "  Primary strength: Financial and procedure data\n", "  Main limitation: Missing clinical details (diagnoses, medications)\n", "  Recommendation: Focus on procedure and cost analysis initially\n"]}], "source": ["# Summary of data readiness for OMOP\n", "print(\"📊 OMOP IMPLEMENTATION READINESS SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "readiness_scores = {\n", "    'PERSON': 60,  # Basic patient ID only\n", "    'VISIT_OCCURRENCE': 85,  # Good encounter data\n", "    'PROCEDURE_OCCURRENCE': 80,  # Good procedure codes and dates\n", "    'COST': 90,  # Excellent financial data\n", "    'PROVIDER': 65,  # Basic provider info\n", "    'DRUG_EXPOSURE': 10,  # No medication data visible\n", "    'CONDITION_OCCURRENCE': 20,  # No diagnosis codes visible\n", "}\n", "\n", "print(\"Table readiness scores (0-100%):\")\n", "for table, score in readiness_scores.items():\n", "    if score >= 70:\n", "        status = \"🟢 HIGH\"\n", "    elif score >= 50:\n", "        status = \"🟡 MEDIUM\" \n", "    else:\n", "        status = \"🔴 LOW\"\n", "    print(f\"  {table}: {score}% {status}\")\n", "\n", "print(f\"\\nOverall assessment:\")\n", "avg_score = sum(readiness_scores.values()) / len(readiness_scores)\n", "print(f\"  Average readiness: {avg_score:.0f}%\")\n", "print(f\"  Primary strength: Financial and procedure data\")\n", "print(f\"  Main limitation: Missing clinical details (diagnoses, medications)\")\n", "print(f\"  Recommendation: Focus on procedure and cost analysis initially\")"]}, {"cell_type": "markdown", "id": "997293b6", "metadata": {}, "source": ["## 9. Key Insights and Next Steps"]}, {"cell_type": "code", "execution_count": 19, "id": "8d703595", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 KEY INSIGHTS\n", "==================================================\n", "📈 Data Volume:\n", "  • 596 patients with 4,999 healthcare activities\n", "  • 1,461 unique encounters/cases\n", "  • Average 8.4 activities per patient\n", "\n", "🩺 Medical Procedures:\n", "  • 769 unique medical codes\n", "  • 64.6% appear to be standard CPT codes\n", "  • 35.4% use local/regional coding\n", "\n", "💰 Financial Impact:\n", "  • Total healthcare value: $705,006.09\n", "  • Average cost per activity: $141.03\n", "\n", "🏥 Healthcare Delivery:\n", "  • 270 healthcare providers\n", "  • 10 healthcare institutions\n", "\n", "🎯 OMOP Transformation Priority:\n", "  1. Start with PROCEDURE_OCCURRENCE (strong code data)\n", "  2. Implement COST table (excellent financial data)\n", "  3. Add VISIT_OCCURRENCE (good encounter structure)\n", "  4. Create basic PERSON records (patient IDs only)\n", "  5. Later: enhance with external code mappings\n"]}], "source": ["print(\"🎯 KEY INSIGHTS\")\n", "print(\"=\" * 50)\n", "\n", "print(\"📈 Data Volume:\")\n", "print(f\"  • {unique_patients:,} patients with {total_activities:,} healthcare activities\")\n", "print(f\"  • {unique_cases:,} unique encounters/cases\")\n", "print(f\"  • Average {total_activities/unique_patients:.1f} activities per patient\")\n", "\n", "print(f\"\\n🩺 Medical Procedures:\")\n", "print(f\"  • {unique_codes:,} unique medical codes\")\n", "print(f\"  • {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes\")\n", "print(f\"  • {(len(df) - cpt_like_codes)/len(df)*100:.1f}% use local/regional coding\")\n", "\n", "if 'gross' in df.columns:\n", "    total_value = df['gross'].sum()\n", "    print(f\"\\n💰 Financial Impact:\")\n", "    print(f\"  • Total healthcare value: ${total_value:,.2f}\")\n", "    print(f\"  • Average cost per activity: ${df['gross'].mean():.2f}\")\n", "\n", "print(f\"\\n🏥 Healthcare Delivery:\")\n", "if 'clinician' in df.columns:\n", "    print(f\"  • {unique_clinicians:,} healthcare providers\")\n", "if 'institution_name' in df.columns:\n", "    print(f\"  • {unique_institutions:,} healthcare institutions\")\n", "\n", "print(f\"\\n🎯 OMOP Transformation Priority:\")\n", "print(f\"  1. Start with PROCEDURE_OCCURRENCE (strong code data)\")\n", "print(f\"  2. Implement COST table (excellent financial data)\")\n", "print(f\"  3. Add VISIT_OCCURRENCE (good encounter structure)\")\n", "print(f\"  4. Create basic PERSON records (patient IDs only)\")\n", "print(f\"  5. Later: enhance with external code mappings\")"]}, {"cell_type": "markdown", "id": "06f9e7c1", "metadata": {}, "source": ["## Conclusion\n", "\n", "This Abu Dhabi claims dataset provides a solid foundation for OMOP CDM implementation, particularly strong in:\n", "\n", "- **Procedure tracking** with medical codes and descriptions\n", "- **Financial analysis** with comprehensive cost information  \n", "- **Encounter management** with clear case/visit structure\n", "- **Provider identification** with clinician information\n", "\n", "**Recommended approach:**\n", "1. Begin with procedure and cost analysis (highest data quality)\n", "2. Use external vocabularies to map local codes to OMOP concepts\n", "3. Implement core OMOP tables incrementally\n", "4. Focus on claims-based use cases initially\n", "\n", "**Next steps:**\n", "- Map procedure codes to OMOP concept IDs using vocabularies\n", "- Design ETL pipeline for priority OMOP tables\n", "- Validate data quality for production use"]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}