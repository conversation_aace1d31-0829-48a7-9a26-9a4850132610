# Setup environment
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Load YOUR real dataset
data_path = "../../../../../data/real_test_datasets/claim_anonymized.csv"
claims_df = pd.read_csv(data_path)
print(f"📊 Dataset shape: {claims_df.shape}")

# Preview data
print("🔍 First 3 rows of YOUR dataset (transposed for better view):")
print("="*70)
claims_df.head(3).T

# 🎯 OMOP READINESS ANALYSIS - What Can We Actually Map?
print("🎯 OMOP READINESS ANALYSIS - What Can We Actually Map?")
print("=" * 60)

# Key statistics from your actual data
print(f"📊 Your Dataset Overview:")
print(f"   • Total medical activities: {len(claims_df):,}")
print(f"   • Unique patients: {claims_df['aio_patient_id'].nunique():,}")
print(f"   • Unique encounters: {claims_df['case'].nunique():,}")
print(f"   • Date range: {claims_df['encounter_start_date'].min()} to {claims_df['encounter_start_date'].max()}")
print(f"   • Healthcare facilities: {claims_df['institution_name'].nunique():,}")

# Analyze what we can map to OMOP immediately
print(f"\n✅ WHAT WE CAN MAP TO OMOP (High Confidence):")
cpt_count = claims_df[claims_df['act_type_desc'] == 'CPT'].shape[0]
cpt_percentage = (cpt_count / len(claims_df)) * 100
print(f"   🎯 CPT procedures: {cpt_count:,} records ({cpt_percentage:.1f}%)")
print(f"   💰 Financial data: {len(claims_df):,} records (100%)")
print(f"   🏥 Patient visits: {claims_df['case'].nunique():,} encounters (100%)")
print(f"   👥 Patient IDs: {claims_df['aio_patient_id'].nunique():,} patients (100%)")

# Show what we'll skip for now
uae_count = claims_df[claims_df['act_type_desc'] == 'Drug'].shape[0]
uae_percentage = (uae_count / len(claims_df)) * 100
print(f"\n⚠️ WHAT WE'LL SKIP (For Now):")
print(f"   ❌ UAE local drug codes: {uae_count:,} records ({uae_percentage:.1f}%)")
print(f"   ❌ Patient demographics: Not available in dataset")
print(f"   ❌ Diagnosis codes (ICD-10): Not available in dataset")

print(f"\n💡 KEY INSIGHT: We can create a working OMOP database with {cpt_percentage:.1f}% of your data!")
print(f"This is perfect for learning OMOP fundamentals with real data.")

# Show sample of what we CAN work with
print(f"📋 Sample CPT Records (Ready for OMOP Mapping):")
cpt_sample = claims_df[claims_df['act_type_desc'] == 'CPT'][['aio_patient_id', 'case', 'code_activity', 'activity_desc', 'gross']].head(5)
display(cpt_sample)

print(f"\n🔍 Understanding the Data Structure:")
print(f"• Each row = One medical activity (procedure, lab test, consultation)")
print(f"• Multiple activities can belong to the same visit (case)")
print(f"• Multiple visits can belong to the same patient (aio_patient_id)")
print(f"• CPT codes are international standards - perfect for OMOP!")

# Let's see how YOUR data maps to OMOP domains
print("🗺️ YOUR DATA → OMOP DOMAIN MAPPING")
print("=" * 50)

# Analyze your data for OMOP domains
print("\n👤 PERSON Domain (Patient Information):")
print(f"   • Available: Patient IDs ({claims_df['aio_patient_id'].nunique():,} unique)")
print(f"   • Missing: Age, gender, race (not in your dataset)")
print(f"   • OMOP Strategy: Create persons with 'unknown' demographics")

print("\n🏥 VISIT_OCCURRENCE Domain (Healthcare Encounters):")
print(f"   • Available: {claims_df['case'].nunique():,} unique encounters")
print(f"   • Available: Start/end dates, encounter types")
print(f"   • OMOP Strategy: Direct mapping - excellent data quality")

print("\n💊 PROCEDURE_OCCURRENCE Domain (Medical Procedures):")
cpt_procedures = claims_df[claims_df['act_type_desc'] == 'CPT']
print(f"   • Available: {len(cpt_procedures):,} CPT procedures")
print(f"   • Available: Procedure descriptions and dates")
print(f"   • OMOP Strategy: Map CPT codes to OMOP concept_ids")

print("\n💰 COST Domain (Financial Information):")
print(f"   • Available: Complete cost data for all {len(claims_df):,} records")
print(f"   • Available: Gross, net, patient share, payer info")
print(f"   • OMOP Strategy: Direct mapping - perfect data quality")

print(f"\n📊 OMOP Readiness Summary:")
print(f"   ✅ PERSON: {claims_df['aio_patient_id'].nunique():,} patients (basic info only)")
print(f"   ✅ VISIT_OCCURRENCE: {claims_df['case'].nunique():,} encounters (excellent)")
print(f"   ✅ PROCEDURE_OCCURRENCE: {len(cpt_procedures):,} procedures (CPT only)")
print(f"   ✅ COST: {len(claims_df):,} cost records (perfect)")

# Show concrete examples of the transformation
print("🔍 CONCRETE TRANSFORMATION EXAMPLES")
print("=" * 50)

# Take one patient's data as example
sample_patient = claims_df['aio_patient_id'].iloc[0]
patient_data = claims_df[claims_df['aio_patient_id'] == sample_patient].head(3)

print(f"\n📋 Example: Patient {sample_patient}")
print(f"Raw CSV data:")
display(patient_data[['aio_patient_id', 'case', 'code_activity', 'activity_desc', 'gross', 'encounter_start_date']])

print(f"\n🎯 How this becomes OMOP:")
print(f"\n1️⃣ PERSON table:")
print(f"   person_id: {sample_patient}")
print(f"   gender_concept_id: 0 (unknown)")
print(f"   birth_datetime: NULL (not available)")

for idx, row in patient_data.iterrows():
    print(f"\n2️⃣ VISIT_OCCURRENCE table (Visit {row['case']}):")
    print(f"   visit_occurrence_id: {row['case']}")
    print(f"   person_id: {row['aio_patient_id']}")
    print(f"   visit_start_date: {row['encounter_start_date']}")
    
    print(f"\n3️⃣ PROCEDURE_OCCURRENCE table:")
    print(f"   procedure_occurrence_id: {row['activity_id']}")
    print(f"   person_id: {row['aio_patient_id']}")
    print(f"   visit_occurrence_id: {row['case']}")
    print(f"   procedure_source_value: {row['code_activity']}")
    
    print(f"\n4️⃣ COST table:")
    print(f"   cost_id: {row['activity_id']}")
    print(f"   cost_domain_id: 'Procedure'")
    print(f"   total_charge: {row['gross']}")
    break  # Show only first record for clarity

print(f"\n💡 Key Insight: One CSV row becomes multiple OMOP table entries!")

# Database setup - simple and educational
import sqlalchemy as sa
from sqlalchemy import create_engine, text, Column, Integer, String, Date, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Database configuration - based on your actual setup
DB_CONFIG = {
    'host': 'localhost',
    'port': '5432',  # Standard PostgreSQL port
    'database': 'omop_abu_dhabi',  # Database we created
    'username': 'jaimepm',  # Your macOS username
    'password': '',  # No password for local macOS setup
    'schema': 'public'
}

# Create connection string (handle empty password)
if DB_CONFIG['password']:
    connection_string = f"postgresql://{DB_CONFIG['username']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
else:
    connection_string = f"postgresql://{DB_CONFIG['username']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

print("🔧 Database Configuration:")
print(f"  Host: {DB_CONFIG['host']}")
print(f"  Port: {DB_CONFIG['port']}")
print(f"  Database: {DB_CONFIG['database']}")
print(f"  Username: {DB_CONFIG['username']}")
print("✅ This matches your PostgreSQL setup!")

# Test database connection
try:
    engine = create_engine(connection_string)
    
    # Test connection
    with engine.connect() as conn:
        result = conn.execute(text("SELECT version();"))
        version = result.fetchone()[0]
        
    print("✅ Database connection successful!")
    print(f"📊 PostgreSQL version: {version.split(',')[0]}")
    
except Exception as e:
    print("❌ Database connection failed!")
    print(f"Error: {str(e)}")
    print("\n🔧 Troubleshooting steps:")
    print("1. Make sure PostgreSQL is installed and running")
    print("2. Check username and password")
    print("3. Create database 'omop_abu_dhabi' if it doesn't exist")
    engine = None

# Define OMOP table structures - based on our variable classification
Base = declarative_base()

class Person(Base):
    """PERSON table - patient information (HIGH priority variables)"""
    __tablename__ = 'person'
    
    person_id = Column(String, primary_key=True)  # aio_patient_id
    gender_concept_id = Column(Integer, default=0)  # 0 = unknown (not available)
    year_of_birth = Column(Integer, nullable=True)  # Not available in dataset
    birth_datetime = Column(DateTime, nullable=True)  # Not available in dataset
    race_concept_id = Column(Integer, default=0)  # 0 = unknown (not available)
    ethnicity_concept_id = Column(Integer, default=0)  # 0 = unknown (not available)
    person_source_value = Column(String)  # Original patient identifier

class VisitOccurrence(Base):
    """VISIT_OCCURRENCE table - healthcare encounters (HIGH priority variables)"""
    __tablename__ = 'visit_occurrence'
    
    visit_occurrence_id = Column(String, primary_key=True)  # case
    person_id = Column(String)  # aio_patient_id (links to person)
    visit_concept_id = Column(Integer, default=9202)  # 9202 = Outpatient Visit
    visit_start_date = Column(Date)  # encounter_start_date
    visit_end_date = Column(Date)  # encounter_end_date
    visit_type_concept_id = Column(Integer, default=44818517)  # EHR encounter record
    provider_id = Column(String)  # provider_id (MEDIUM priority)
    care_site_id = Column(String)  # receiver_id (facility)
    visit_source_value = Column(String)  # case_type
    visit_source_concept_id = Column(Integer, default=0)
    admitting_source_value = Column(String)  # encounter_start_type_desc

class ProcedureOccurrence(Base):
    """PROCEDURE_OCCURRENCE table - medical procedures (HIGH priority variables)"""
    __tablename__ = 'procedure_occurrence'
    
    procedure_occurrence_id = Column(String, primary_key=True)  # activity_id
    person_id = Column(String)  # aio_patient_id (links to person)
    visit_occurrence_id = Column(String)  # case (links to visit)
    procedure_concept_id = Column(Integer, default=0)  # Will map CPT codes later
    procedure_date = Column(Date)  # start_activity_date
    procedure_datetime = Column(DateTime)  # start_activity_date with time
    procedure_type_concept_id = Column(Integer, default=38000275)  # EHR order list
    modifier_concept_id = Column(Integer, default=0)  # reference_activity
    quantity = Column(Float)  # activity_quantity
    provider_id = Column(String)  # clinician
    procedure_source_value = Column(String)  # code_activity (CPT code)
    procedure_source_concept_id = Column(Integer, default=0)
    modifier_source_value = Column(String)  # act_type_desc

class Cost(Base):
    """COST table - financial information (HIGH priority variables)"""
    __tablename__ = 'cost'
    
    cost_id = Column(String, primary_key=True)  # activity_id
    cost_event_id = Column(String)  # activity_id (same as cost_id)
    cost_domain_id = Column(String, default='Procedure')  # Domain of the cost event
    cost_type_concept_id = Column(Integer, default=5032)  # 5032 = Claim
    currency_concept_id = Column(Integer, default=44818568)  # AED currency
    total_charge = Column(Float)  # gross (total charges)
    total_cost = Column(Float)  # net (net charges to payer)
    total_paid = Column(Float)  # payment_amount (actual payment)
    paid_by_payer = Column(Float)  # net - patient_share
    paid_by_patient = Column(Float)  # patient_share
    paid_patient_copay = Column(Float)  # patient_share (copay)
    paid_patient_coinsurance = Column(Float, default=0)  # Not available
    paid_patient_deductible = Column(Float, default=0)  # Not available
    paid_by_primary = Column(Float)  # payment_amount
    paid_ingredient_cost = Column(Float, default=0)  # Not applicable for procedures
    paid_dispensing_fee = Column(Float, default=0)  # Not applicable for procedures
    payer_plan_period_id = Column(String)  # insurance_plan_id
    amount_allowed = Column(Float)  # net (allowed amount)
    revenue_code_concept_id = Column(Integer, default=0)  # Could map from activity type
    revenue_code_source_value = Column(String)  # type_activity

# Additional table for provider information (MEDIUM priority)
class Provider(Base):
    """PROVIDER table - healthcare provider information (MEDIUM priority variables)"""
    __tablename__ = 'provider'
    
    provider_id = Column(String, primary_key=True)  # clinician
    provider_name = Column(String)  # clinician_name
    npi = Column(String)  # Not available
    dea = Column(String)  # Not available
    specialty_concept_id = Column(Integer, default=0)  # Not available
    care_site_id = Column(String)  # institution_name
    year_of_birth = Column(Integer)  # Not available
    gender_concept_id = Column(Integer, default=0)  # Not available
    provider_source_value = Column(String)  # clinician (original ID)
    specialty_source_value = Column(String)  # Not available
    specialty_source_concept_id = Column(Integer, default=0)
    gender_source_value = Column(String)  # Not available
    gender_source_concept_id = Column(Integer, default=0)

print("📋 ENHANCED OMOP Table Definitions Created:")
print("   • PERSON - Patient information (basic, demographics missing)")
print("   • VISIT_OCCURRENCE - Healthcare encounters (complete)")
print("   • PROCEDURE_OCCURRENCE - Medical procedures (CPT focus)")
print("   • COST - Financial information (comprehensive)")
print("   • PROVIDER - Healthcare provider information (basic)")
print("\n💡 Tables designed based on our 54-variable classification analysis!")
print("🎯 Captures HIGH and MEDIUM priority variables from your dataset")

# Create the tables in the database
if engine:
    try:
        # Drop existing tables if they exist (for clean start)
        Base.metadata.drop_all(engine)
        print("🗑️ Dropped existing tables (if any)")
        
        # Create new tables
        Base.metadata.create_all(engine)
        print("✅ Created OMOP tables successfully!")
        
        # Verify tables were created
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """))
            tables = [row[0] for row in result.fetchall()]
        
        print(f"\n📊 Tables created: {', '.join(tables)}")
        
    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
else:
    print("❌ Cannot create tables - no database connection")

# Prepare data for OMOP transformation - focus on CPT codes only
print("🔄 PREPARING DATA FOR OMOP TRANSFORMATION")
print("=" * 50)

# Filter to CPT codes only (our high-confidence data)
cpt_data = claims_df[claims_df['act_type_desc'] == 'CPT'].copy()
print(f"📊 Working with {len(cpt_data):,} CPT records ({len(cpt_data)/len(claims_df)*100:.1f}% of total data)")

# Convert dates to proper format
cpt_data['encounter_start_date'] = pd.to_datetime(cpt_data['encounter_start_date'], format='%d/%m/%Y')
cpt_data['encounter_end_date'] = pd.to_datetime(cpt_data['encounter_end_date'], format='%d/%m/%Y')

print(f"\n📅 Date range: {cpt_data['encounter_start_date'].min().date()} to {cpt_data['encounter_start_date'].max().date()}")
print(f"👥 Unique patients: {cpt_data['aio_patient_id'].nunique():,}")
print(f"🏥 Unique encounters: {cpt_data['case'].nunique():,}")
print(f"💊 Unique CPT codes: {cpt_data['code_activity'].nunique():,}")

# Show sample of prepared data
print(f"\n📋 Sample prepared data:")
sample_cols = ['aio_patient_id', 'case', 'activity_id', 'code_activity', 'activity_desc', 'gross', 'encounter_start_date']
display(cpt_data[sample_cols].head(3))

# Simple ETL functions - easy to understand
def create_person_records(data):
    """Create PERSON table records from unique patients (HIGH priority variables)"""
    unique_patients = data['aio_patient_id'].unique()
    
    person_records = []
    for patient_id in unique_patients:
        person_record = {
            'person_id': patient_id,  # aio_patient_id
            'gender_concept_id': 0,  # Unknown (not available in dataset)
            'year_of_birth': None,   # Not available in dataset
            'birth_datetime': None,  # Not available in dataset
            'race_concept_id': 0,    # Unknown (not available in dataset)
            'ethnicity_concept_id': 0,  # Unknown (not available in dataset)
            'person_source_value': patient_id  # Original patient identifier
        }
        person_records.append(person_record)
    
    return person_records

def create_visit_records(data):
    """Create VISIT_OCCURRENCE table records from unique encounters (HIGH priority variables)"""
    # Get unique visits (one record per case) with additional fields
    visit_data = data.groupby('case').agg({
        'aio_patient_id': 'first',
        'encounter_start_date': 'first',
        'encounter_end_date': 'first',
        'case_type': 'first',
        'provider_id': 'first',  # MEDIUM priority
        'receiver_id': 'first',  # Care site (facility)
        'encounter_start_type_desc': 'first'  # Admitting source
    }).reset_index()
    
    visit_records = []
    for _, row in visit_data.iterrows():
        visit_record = {
            'visit_occurrence_id': row['case'],  # case
            'person_id': row['aio_patient_id'],  # aio_patient_id
            'visit_concept_id': 9202,  # 9202 = Outpatient Visit
            'visit_start_date': row['encounter_start_date'].date(),  # encounter_start_date
            'visit_end_date': row['encounter_end_date'].date(),  # encounter_end_date
            'visit_type_concept_id': 44818517,  # EHR encounter record
            'provider_id': row['provider_id'],  # provider_id (MEDIUM priority)
            'care_site_id': row['receiver_id'],  # receiver_id (facility)
            'visit_source_value': row['case_type'],  # case_type
            'visit_source_concept_id': 0,
            'admitting_source_value': row['encounter_start_type_desc']  # encounter_start_type_desc
        }
        visit_records.append(visit_record)
    
    return visit_records

def create_procedure_records(data):
    """Create PROCEDURE_OCCURRENCE table records from CPT procedures (HIGH priority variables)"""
    procedure_records = []
    
    for _, row in data.iterrows():
        # Convert start_activity_date if available, otherwise use encounter_start_date
        procedure_date = row['start_activity_date'] if pd.notna(row['start_activity_date']) else row['encounter_start_date']
        if isinstance(procedure_date, str):
            procedure_date = pd.to_datetime(procedure_date, format='%d/%m/%Y')
        
        procedure_record = {
            'procedure_occurrence_id': row['activity_id'],  # activity_id
            'person_id': row['aio_patient_id'],  # aio_patient_id
            'visit_occurrence_id': row['case'],  # case
            'procedure_concept_id': 0,  # Will map CPT codes later
            'procedure_date': procedure_date.date(),  # start_activity_date or encounter_start_date
            'procedure_datetime': procedure_date,  # start_activity_date with time
            'procedure_type_concept_id': 38000275,  # EHR order list
            'modifier_concept_id': 0,  # reference_activity (could be mapped)
            'quantity': float(row['activity_quantity']) if pd.notna(row['activity_quantity']) else 1.0,  # activity_quantity
            'provider_id': row['clinician'],  # clinician
            'procedure_source_value': row['code_activity'],  # code_activity (CPT code)
            'procedure_source_concept_id': 0,
            'modifier_source_value': row['act_type_desc']  # act_type_desc
        }
        procedure_records.append(procedure_record)
    
    return procedure_records

def create_cost_records(data):
    """Create COST table records from financial data (HIGH priority variables)"""
    cost_records = []
    
    for _, row in data.iterrows():
        cost_record = {
            'cost_id': row['activity_id'],  # activity_id
            'cost_event_id': row['activity_id'],  # activity_id (same as cost_id)
            'cost_domain_id': 'Procedure',  # Domain of the cost event
            'cost_type_concept_id': 5032,  # 5032 = Claim
            'currency_concept_id': 44818568,  # AED currency
            'total_charge': float(row['gross']),  # gross (total charges)
            'total_cost': float(row['net']),  # net (net charges to payer)
            'total_paid': float(row['payment_amount']),  # payment_amount (actual payment)
            'paid_by_payer': float(row['net']) - float(row['patient_share']),  # net - patient_share
            'paid_by_patient': float(row['patient_share']),  # patient_share
            'paid_patient_copay': float(row['patient_share']),  # patient_share (copay)
            'paid_patient_coinsurance': 0.0,  # Not available in dataset
            'paid_patient_deductible': 0.0,  # Not available in dataset
            'paid_by_primary': float(row['payment_amount']),  # payment_amount
            'paid_ingredient_cost': 0.0,  # Not applicable for procedures
            'paid_dispensing_fee': 0.0,  # Not applicable for procedures
            'payer_plan_period_id': row['insurance_plan_id'],  # insurance_plan_id
            'amount_allowed': float(row['net']),  # net (allowed amount)
            'revenue_code_concept_id': 0,  # Could map from activity type
            'revenue_code_source_value': str(row['type_activity'])  # type_activity
        }
        cost_records.append(cost_record)
    
    return cost_records

def create_provider_records(data):
    """Create PROVIDER table records from unique clinicians (MEDIUM priority variables)"""
    # Get unique providers (one record per clinician)
    provider_data = data.groupby('clinician').agg({
        'clinician_name': 'first',
        'institution_name': 'first'
    }).reset_index()
    
    provider_records = []
    for _, row in provider_data.iterrows():
        provider_record = {
            'provider_id': row['clinician'],  # clinician
            'provider_name': row['clinician_name'],  # clinician_name
            'npi': None,  # Not available in dataset
            'dea': None,  # Not available in dataset
            'specialty_concept_id': 0,  # Not available in dataset
            'care_site_id': row['institution_name'],  # institution_name
            'year_of_birth': None,  # Not available in dataset
            'gender_concept_id': 0,  # Not available in dataset
            'provider_source_value': row['clinician'],  # clinician (original ID)
            'specialty_source_value': None,  # Not available in dataset
            'specialty_source_concept_id': 0,
            'gender_source_value': None,  # Not available in dataset
            'gender_source_concept_id': 0
        }
        provider_records.append(provider_record)
    
    return provider_records

print("✅ ENHANCED ETL functions defined!")
print("📋 Functions created based on our 54-variable classification:")
print("   • create_person_records() - Transform patients (HIGH priority)")
print("   • create_visit_records() - Transform encounters (HIGH priority)")
print("   • create_procedure_records() - Transform procedures (HIGH priority)")
print("   • create_cost_records() - Transform financial data (HIGH priority)")
print("   • create_provider_records() - Transform providers (MEDIUM priority)")
print("\n🎯 Now capturing more variables according to our classification!")

# Transform the data using our ETL functions
print("🔄 TRANSFORMING YOUR DATA TO OMOP FORMAT")
print("=" * 50)

# Create OMOP records
print("1️⃣ Creating PERSON records...")
person_records = create_person_records(cpt_data)
print(f"   ✅ Created {len(person_records):,} person records")

print("\n2️⃣ Creating VISIT_OCCURRENCE records...")
visit_records = create_visit_records(cpt_data)
print(f"   ✅ Created {len(visit_records):,} visit records")

print("\n3️⃣ Creating PROCEDURE_OCCURRENCE records...")
procedure_records = create_procedure_records(cpt_data)
print(f"   ✅ Created {len(procedure_records):,} procedure records")

print("\n4️⃣ Creating COST records...")
cost_records = create_cost_records(cpt_data)
print(f"   ✅ Created {len(cost_records):,} cost records")

print("\n5️⃣ Creating PROVIDER records...")
provider_records = create_provider_records(cpt_data)
print(f"   ✅ Created {len(provider_records):,} provider records")

print(f"\n📊 ENHANCED Transformation Summary:")
print(f"   • {len(person_records):,} patients")
print(f"   • {len(visit_records):,} encounters")
print(f"   • {len(procedure_records):,} procedures")
print(f"   • {len(cost_records):,} cost records")
print(f"   • {len(provider_records):,} providers")
print(f"\n💡 Ready to load into enhanced OMOP database!")
print(f"🎯 Capturing {len(person_records) + len(visit_records) + len(procedure_records) + len(cost_records) + len(provider_records):,} total OMOP records!")

# Load data into OMOP database
if engine:
    try:
        Session = sessionmaker(bind=engine)
        session = Session()
        
        print("💾 LOADING DATA INTO OMOP DATABASE")
        print("=" * 50)
        
        # Load PERSON records
        print("1️⃣ Loading PERSON records...")
        for record in person_records:
            person = Person(**record)
            session.merge(person)  # Use merge to handle duplicates
        session.commit()
        print(f"   ✅ Loaded {len(person_records):,} person records")
        
        # Load VISIT_OCCURRENCE records
        print("\n2️⃣ Loading VISIT_OCCURRENCE records...")
        for record in visit_records:
            visit = VisitOccurrence(**record)
            session.merge(visit)
        session.commit()
        print(f"   ✅ Loaded {len(visit_records):,} visit records")
        
        # Load PROCEDURE_OCCURRENCE records
        print("\n3️⃣ Loading PROCEDURE_OCCURRENCE records...")
        for record in procedure_records:
            procedure = ProcedureOccurrence(**record)
            session.merge(procedure)
        session.commit()
        print(f"   ✅ Loaded {len(procedure_records):,} procedure records")
        
        # Load COST records
        print("\n4️⃣ Loading COST records...")
        for record in cost_records:
            cost = Cost(**record)
            session.merge(cost)
        session.commit()
        print(f"   ✅ Loaded {len(cost_records):,} cost records")
        
        session.close()
        print(f"\n🎉 SUCCESS! Your Abu Dhabi data is now in OMOP format!")
        
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        if 'session' in locals():
            session.rollback()
            session.close()
else:
    print("❌ Cannot load data - no database connection")

# Validate the OMOP database
if engine:
    print("✅ VALIDATING YOUR OMOP DATABASE")
    print("=" * 50)
    
    with engine.connect() as conn:
        # Check record counts
        print("📊 Record Counts:")
        
        person_count = conn.execute(text("SELECT COUNT(*) FROM person")).fetchone()[0]
        print(f"   👥 PERSON: {person_count:,} patients")
        
        visit_count = conn.execute(text("SELECT COUNT(*) FROM visit_occurrence")).fetchone()[0]
        print(f"   🏥 VISIT_OCCURRENCE: {visit_count:,} encounters")
        
        procedure_count = conn.execute(text("SELECT COUNT(*) FROM procedure_occurrence")).fetchone()[0]
        print(f"   💊 PROCEDURE_OCCURRENCE: {procedure_count:,} procedures")
        
        cost_count = conn.execute(text("SELECT COUNT(*) FROM cost")).fetchone()[0]
        print(f"   💰 COST: {cost_count:,} cost records")
        
        # Check data integrity
        print(f"\n🔍 Data Integrity Checks:")
        
        # Check if all procedures have corresponding visits
        orphan_procedures = conn.execute(text("""
            SELECT COUNT(*) 
            FROM procedure_occurrence p 
            LEFT JOIN visit_occurrence v ON p.visit_occurrence_id = v.visit_occurrence_id 
            WHERE v.visit_occurrence_id IS NULL
        """)).fetchone()[0]
        
        if orphan_procedures == 0:
            print(f"   ✅ All procedures linked to visits")
        else:
            print(f"   ⚠️ {orphan_procedures} procedures without visits")
        
        # Check if all visits have corresponding persons
        orphan_visits = conn.execute(text("""
            SELECT COUNT(*) 
            FROM visit_occurrence v 
            LEFT JOIN person p ON v.person_id = p.person_id 
            WHERE p.person_id IS NULL
        """)).fetchone()[0]
        
        if orphan_visits == 0:
            print(f"   ✅ All visits linked to patients")
        else:
            print(f"   ⚠️ {orphan_visits} visits without patients")
        
        # Show sample data
        print(f"\n📋 Sample OMOP Data:")
        
        sample_query = text("""
            SELECT 
                p.person_id,
                v.visit_occurrence_id,
                v.visit_start_date,
                proc.procedure_source_value,
                c.total_charge
            FROM person p
            JOIN visit_occurrence v ON p.person_id = v.person_id
            JOIN procedure_occurrence proc ON v.visit_occurrence_id = proc.visit_occurrence_id
            JOIN cost c ON proc.procedure_occurrence_id = c.cost_id
            LIMIT 3
        """)
        
        result = conn.execute(sample_query)
        sample_data = result.fetchall()
        
        for row in sample_data:
            print(f"   Patient {row[0]} → Visit {row[1]} → Procedure {row[3]} → Cost ${row[4]:.2f}")
        
        print(f"\n🎉 VALIDATION COMPLETE - Your OMOP database is working!")
else:
    print("❌ Cannot validate - no database connection")